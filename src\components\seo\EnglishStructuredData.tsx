import Script from 'next/script';
import {
  generateEnglishSoftwareStructuredData,
  generateEnglishOrganizationStructuredData,
  generateEnglishWebsiteStructuredData,
  generateEnglishFAQStructuredData,
} from '@/utils/seo-en';

export function EnglishStructuredData() {
  const softwareData = generateEnglishSoftwareStructuredData();
  const organizationData = generateEnglishOrganizationStructuredData();
  const websiteData = generateEnglishWebsiteStructuredData();
  const faqData = generateEnglishFAQStructuredData();

  return (
    <>
      <Script
        id="structured-data-software-en"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(softwareData),
        }}
      />
      <Script
        id="structured-data-organization-en"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationData),
        }}
      />
      <Script
        id="structured-data-website-en"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteData),
        }}
      />
      <Script
        id="structured-data-faq-en"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqData),
        }}
      />
    </>
  );
}
