import {
  generateTokens,
  hashPassword,
  setAuthCookies,
  validateEmail,
  validatePassword,
} from '@/lib/auth';
import { createUser, createUserSession, getUserByEmail, logUserActivity } from '@/lib/user-db';
import { verifyVerificationCode, invalidateVerificationCode } from '@/lib/verification-code-db';
import { error, success } from '@/lib/api-helpers';
import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const { email, username, password, verificationCode } = await request.json();

    // 验证输入
    if (!email || !username || !password || !verificationCode) {
      return error('请填写所有必填字段，包括邮箱验证码', 400);
    }

    // 验证邮箱格式
    if (!validateEmail(email)) {
      return error('请输入有效的邮箱地址', 400);
    }

    // 验证密码强度
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.valid) {
      return error(passwordValidation.message, 400);
    }

    // 验证用户名长度
    if (username.length < 2 || username.length > 50) {
      return error('用户名长度必须在2-50个字符之间', 400);
    }

    // 验证验证码格式（6位数字）
    if (!/^\d{6}$/.test(verificationCode)) {
      return error('验证码格式错误，请输入6位数字', 400);
    }

    // 检查邮箱是否已存在
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      return error('该邮箱已被注册', 409);
    }

    // 验证邮箱验证码
    const verifyResult = await verifyVerificationCode(email, verificationCode, 'register');
    if (verifyResult !== 'success') {
      let errorMessage = '验证码验证失败';
      switch (verifyResult) {
        case 'invalid':
          errorMessage = '验证码错误';
          break;
        case 'expired':
          errorMessage = '验证码已过期，请重新获取';
          break;
        case 'used':
          errorMessage = '验证码已使用，请重新获取';
          break;
        case 'max_attempts':
          errorMessage = '验证失败次数过多，请重新获取验证码';
          break;
      }
      return error(errorMessage, 400);
    }

    // 加密密码
    const passwordHash = await hashPassword(password);

    // 创建用户
    const userId = await createUser({
      email,
      username,
      password_hash: passwordHash,
      verification_token: verificationCode,
    });

    if (!userId) {
      return error('注册失败，请稍后重试', 500);
    }

    // 获取客户端IP和User-Agent
    const ipAddress = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // 记录注册活动
    await logUserActivity(userId, 'register', ipAddress, userAgent, { 
      email, 
      username,
      verificationMethod: 'email_code'
    });

    // 生成token并设置cookie
    const { accessToken, refreshToken } = await generateTokens(userId, email);

    // 创建会话
    const sessionId = uuidv4();
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15分钟
    const refreshExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7天

    await createUserSession(
      userId,
      sessionId,
      accessToken,
      refreshToken,
      expiresAt,
      refreshExpiresAt,
      ipAddress as string,
      userAgent
    );

    // 设置认证cookie
    await setAuthCookies(accessToken, refreshToken);

    // 清理已使用的验证码（可选，因为验证时已经标记为已使用）
    await invalidateVerificationCode(email, 'register');

    return success(
      {
        user: {
          id: userId,
          email,
          username,
          email_verified: true, // 通过验证码验证，邮箱已验证
        },
        sessionId,
      },
      '注册成功，欢迎使用 TradingAgent！'
    );
  } catch (err: any) {
    console.error('Registration error:', err);
    return error('服务器内部错误', 500);
  }
}
