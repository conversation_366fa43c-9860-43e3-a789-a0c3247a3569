/*
 * @Author: ezrealbb <EMAIL>
 * @Date: 2025-08-26 19:36:26
 * @LastEditors: ezrealbb <EMAIL>
 * @LastEditTime: 2025-08-27 00:41:52
 * @FilePath: \trading-agents-frontend\src\i18n.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { getRequestConfig } from 'next-intl/server';
import { notFound } from 'next/navigation';

// Can be imported from a shared config
const locales = ['zh', 'en'];
const defaultLocale = 'zh';

export default getRequestConfig(async ({ locale }) => {
  console.log('Initial locale in getRequestConfig:', locale);

  // 如果 locale 未定义或无效，使用默认语言
  const validLocale = locale && locales.includes(locale as any) ? locale : defaultLocale;

  if (!locales.includes(locale as any) && locale) {
    console.log('Invalid locale:', locale);
    notFound();
  }

  return {
    messages: (await import(`./messages/${validLocale}.json`)).default,
    locale: validLocale as string,
  };
});
