'use client';

import AnimatedMultiSelect from '@/components/ui/AnimatedMultiSelect';
import AnimatedSelect from '@/components/ui/AnimatedSelect';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { StockBackgroundElements } from '@/components/ui/StockBackgroundElements';
import useUserStore from '@/store/userStore';
import {
  AnalysisPeriod,
  analysisPeriodOptions as apiAnalysisPeriodOptions,
  researchDepthOptions as apiResearchDepthOptions,
  ResearchDepth,
} from '@/types/database';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';

export default function CreateTaskPage() {
  const router = useRouter();
  const { user } = useUserStore();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 表单状态
  const [formData, setFormData] = useState({
    stockSymbol: '',
    analysisType: 'comprehensive' as const,
    timePeriod: '1m' as AnalysisPeriod,
    researchDepth: 'shallow' as ResearchDepth,
    selectedAnalysts: [] as string[],
    customInstructions: '',
  });

  // 分析师选项
  const analystOptions = [
    { value: 'fundamental', label: '基本面分析师', description: '财务数据和价值评估' },
    { value: 'technical', label: '技术分析师', description: '价格走势和交易信号' },
    { value: 'sentiment', label: '情绪分析师', description: '市场情绪和投资者行为' },
    { value: 'news', label: '新闻分析师', description: '新闻事件和市场影响' },
    { value: 'risk', label: '风险管理师', description: '风险评估和控制' },
  ];

  // 分析类型选项
  const analysisTypeOptions = [
    { value: 'comprehensive', label: '综合分析', description: '全方位深度分析' },
    { value: 'quick', label: '快速分析', description: '重点关注核心指标' },
    { value: 'custom', label: '自定义分析', description: '根据需求定制分析' },
  ];

  // 时间周期选项
  const timePeriodOptions = apiAnalysisPeriodOptions.map((option) => ({
    value: option,
    label:
      option === '1d'
        ? '1天'
        : option === '1w'
        ? '1周'
        : option === '1m'
        ? '1个月'
        : option === '3m'
        ? '3个月'
        : option === '6m'
        ? '6个月'
        : option === '1y'
        ? '1年'
        : '自定义',
    description: option === 'custom' ? '自定义时间范围' : `分析过去${option}的数据`,
  }));

  // 研究深度选项
  const researchDepthOptions = apiResearchDepthOptions.map((option) => ({
    value: option,
    label: option === 'shallow' ? '浅度分析' : option === 'medium' ? '中度分析' : '深度分析',
    description:
      option === 'shallow' ? '快速概览' : option === 'medium' ? '标准分析' : '全面深入分析',
  }));

  // 检查用户登录状态
  useEffect(() => {
    if (!user) {
      toast.error('请先登录');
      router.push('/login');
    }
  }, [user, router]);

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id) {
      toast.error('用户未登录');
      return;
    }

    if (!formData.stockSymbol.trim()) {
      toast.error('请输入股票代码');
      return;
    }

    if (formData.selectedAnalysts.length === 0) {
      toast.error('请至少选择一个分析师');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/tasks/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          created_by: user.id,
          ticker: formData.stockSymbol.toUpperCase(),
          analysis_type: formData.analysisType,
          time_period: formData.timePeriod,
          research_depth: formData.researchDepth,
          selected_analysts: formData.selectedAnalysts,
          custom_instructions: formData.customInstructions,
          title: `${formData.stockSymbol.toUpperCase()} - ${formData.analysisType} 分析`,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '创建任务失败');
      }

      const result = await response.json();
      toast.success('任务创建成功！');

      // 跳转到任务列表页面
      router.push('/tasks');
    } catch (error) {
      console.error('创建任务失败:', error);
      toast.error(error instanceof Error ? error.message : '创建任务失败');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理表单字段变化
  const handleFieldChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
      {/* 背景装饰 */}
      <StockBackgroundElements />

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            创建分析任务
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            配置您的专属股票分析任务，让AI专家团队为您提供深度投资洞察
          </p>
        </div>

        {/* 任务创建表单 */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* 股票代码输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                股票代码 *
              </label>
              <input
                type="text"
                value={formData.stockSymbol}
                onChange={(e) => handleFieldChange('stockSymbol', e.target.value)}
                placeholder="例如: AAPL, TSLA, NVDA"
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
              />
            </div>

            {/* 分析类型选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                分析类型
              </label>
              <AnimatedSelect
                options={analysisTypeOptions}
                value={formData.analysisType}
                onChange={(value) => handleFieldChange('analysisType', value)}
                placeholder="选择分析类型"
              />
            </div>

            {/* 时间周期选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                分析时间周期
              </label>
              <AnimatedSelect
                options={timePeriodOptions}
                value={formData.timePeriod}
                onChange={(value) => handleFieldChange('timePeriod', value)}
                placeholder="选择时间周期"
              />
            </div>

            {/* 研究深度选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                研究深度
              </label>
              <AnimatedSelect
                options={researchDepthOptions}
                value={formData.researchDepth}
                onChange={(value) => handleFieldChange('researchDepth', value)}
                placeholder="选择研究深度"
              />
            </div>

            {/* 分析师选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                选择分析师团队 *
              </label>
              <AnimatedMultiSelect
                options={analystOptions}
                selectedValues={formData.selectedAnalysts}
                onChange={(value) => handleFieldChange('selectedAnalysts', value)}
                placeholder="选择参与分析的专家"
              />
            </div>

            {/* 自定义指令 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                自定义分析指令（可选）
              </label>
              <textarea
                value={formData.customInstructions}
                onChange={(e) => handleFieldChange('customInstructions', e.target.value)}
                placeholder="请描述您希望重点关注的分析方向或特殊要求..."
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
              />
            </div>

            {/* 提交按钮 */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                {isSubmitting && <LoadingSpinner size="sm" />}
                <span>{isSubmitting ? '创建中...' : '创建任务'}</span>
              </button>
            </div>
          </form>
        </div>

        {/* 功能说明 */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
              <svg
                className="w-6 h-6 text-blue-600 dark:text-blue-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">智能分析</h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              多维度专业分析，从技术面、基本面、情绪面全方位评估投资价值
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4">
              <svg
                className="w-6 h-6 text-green-600 dark:text-green-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">实时监控</h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              实时跟踪分析进度，透明化展示专家讨论过程和分析结果
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4">
              <svg
                className="w-6 h-6 text-purple-600 dark:text-purple-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">专业建议</h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              基于深度分析提供专业投资建议，帮助您做出明智的投资决策
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
