/*
 * @Author: ezrealbb <EMAIL>
 * @Date: 2025-08-27 00:18:32
 * @LastEditors: ezrealbb <EMAIL>
 * @LastEditTime: 2025-08-27 00:45:40
 * @FilePath: \trading-agents-frontend\src\app\[locale]\layout.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Analytics } from '@/components/analytics/Analytics';
import { NetworkStatus } from '@/components/common/ConnectionStatus';
import { DevTools } from '@/components/common/DevTools';
import { EnvLogger } from '@/components/common/EnvLogger';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { Footer } from '@/components/layout/Footer';
import { Header } from '@/components/layout/Header';
import { Providers } from '@/components/providers';
import { generateMetadataForLocale } from '@/utils/seo-i18n';
import type { Viewport } from 'next';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { Inter } from 'next/font/google';
import { Toaster } from 'react-hot-toast';
import '../globals.css';

const inter = Inter({ subsets: ['latin'] });

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const metadata = generateMetadataForLocale(locale);
  return {
    ...metadata,
    metadataBase: new URL(process.env.FRONTEND_URL || 'http://localhost:3000'),
  };
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  try {
    const { locale } = await params;
    console.log(locale);
    const messages = await getMessages({
      locale,
    });
    return (
      <html lang={locale === 'en' ? 'en-US' : 'zh-CN'}>
        <head>
          <Analytics />
        </head>
        <body className={inter.className}>
          <NextIntlClientProvider messages={messages}>
            <ErrorBoundary>
              <Providers>
                <EnvLogger />
                <DevTools />
                <div className="min-h-screen flex flex-col">
                  <Header />
                  <NetworkStatus />
                  <main className="flex-1 pt-16">{children}</main>
                  <Footer />
                </div>
                <Toaster
                  position="top-right"
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: '#363636',
                      color: '#fff',
                    },
                    success: {
                      duration: 3000,
                      iconTheme: {
                        primary: '#22c55e',
                        secondary: '#fff',
                      },
                    },
                    error: {
                      duration: 5000,
                      iconTheme: {
                        primary: '#ef4444',
                        secondary: '#fff',
                      },
                    },
                  }}
                />
              </Providers>
            </ErrorBoundary>
          </NextIntlClientProvider>
        </body>
      </html>
    );
  } catch (error) {
    console.error('Error fetching messages for locale:', error);
    throw error;
  }
}
