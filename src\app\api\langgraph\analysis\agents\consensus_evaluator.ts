import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { AIMessage } from '@langchain/core/messages';
import { PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';

const consensusEvaluatorPrompt = PromptTemplate.fromTemplate(`
你是一位资深的投资共识评估专家，拥有20年的机构投资经验。你的职责是基于多头和空头研究员的辩论结果，进行客观、全面的共识评估，为最终的投资决策提供平衡的观点和建议。

【评估任务】
股票代码: {ticker}
评估日期: {date}
辩论轮次: {totalRounds}

【辩论历史】
{debateHistory}

【多头最终观点】
{finalBullView}

【空头最终观点】
{finalBearView}

【共识评估框架】
请按照以下专业框架进行全面的共识评估：

1. **观点分歧分析**
   - 识别多空双方的核心分歧点
   - 评估分歧的合理性和重要性
   - 分析分歧背后的假设差异
   - 判断哪些分歧是可调和的

2. **论证强度评估**
   - 评估多头论证的逻辑性和数据支撑
   - 评估空头论证的合理性和风险识别
   - 对比双方论证的说服力
   - 识别论证中的薄弱环节

3. **风险收益平衡**
   - 综合评估上行潜力和下行风险
   - 分析风险收益比的合理性
   - 评估不同时间框架下的投资价值
   - 考虑市场环境和宏观因素

4. **概率权重分配**
   - 为多头情景分配概率权重
   - 为空头情景分配概率权重
   - 为中性情景分配概率权重
   - 基于概率加权计算期望收益

5. **关键变量识别**
   - 识别影响投资结果的关键变量
   - 评估关键变量的可预测性
   - 分析关键变量的敏感性
   - 提出关键变量的监控建议

6. **市场时机考量**
   - 评估当前市场时机的适宜性
   - 分析市场情绪和资金流向
   - 考虑宏观经济和政策环境
   - 判断最佳入场和退出时机

7. **投资策略建议**
   - 基于共识分析提出投资建议
   - 设定合理的仓位配置
   - 制定风险控制措施
   - 提供动态调整策略

8. **共识结论**
   - 总结多空辩论的主要收获
   - 形成平衡的投资观点
   - 提出明确的行动建议
   - 设定关键监控指标

【评估维度】
请对以下维度进行1-10分的量化评估：
- 多头论证强度 (1-10分)
- 空头论证强度 (1-10分)
- 上行潜力 (1-10分)
- 下行风险 (1-10分)
- 投资确定性 (1-10分)
- 时机适宜性 (1-10分)

【输出要求】
- 保持客观中立，避免偏向任何一方
- 基于事实和逻辑进行评估
- 提供量化的概率和评分
- 给出明确的投资建议和理由
- 为后续决策提供清晰的框架
`);

export async function consensusEvaluatorNode(state: typeof TradingAgentAnnotation.State) {
  const { ticker, date, config, research, messages } = state;

  console.log(`[共识评估师] 开始评估投资共识: ${ticker}`);

  try {
    // 检查辩论是否完成
    if (!research?.debateCompleted || !research?.bull || !research?.bear) {
      throw new Error('辩论未完成或研究数据不完整，无法进行共识评估');
    }

    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.nuwaapi.com/v1';

    const llm = new ChatOpenAI({
      modelName: config.deepThinkLLM || 'gpt-4o',
      temperature: 0.1, // 低温度以确保客观性
      apiKey: OPENAI_API_KEY,
      configuration: {
        baseURL: OPENAI_BASE_URL,
      },
    });

    // 准备评估材料
    const debateHistory = formatDebateHistory(research.debateRounds || []);
    const finalBullView = formatFinalView(research.bull);
    const finalBearView = formatFinalView(research.bear);
    const totalRounds = research.debateRounds?.length || 0;

    const prompt = await consensusEvaluatorPrompt.format({
      ticker,
      date,
      totalRounds,
      debateHistory,
      finalBullView,
      finalBearView,
    });

    console.log(`[共识评估师] 正在进行综合共识评估...`);
    const response = await llm.invoke(prompt);
    const evaluationReport = response.content as string;

    // 提取评估结果
    const consensusScores = extractConsensusScores(evaluationReport);
    const probabilityWeights = extractProbabilityWeights(evaluationReport);
    const keyVariables = extractKeyVariables(evaluationReport);
    const investmentRecommendation = extractInvestmentRecommendation(evaluationReport);
    const riskRewardRatio = calculateRiskRewardRatio(consensusScores, probabilityWeights);

    // 生成结构化摘要
    const summary = generateConsensusSummary(
      evaluationReport,
      investmentRecommendation,
      consensusScores,
      probabilityWeights
    );

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【共识评估师报告】\n\n${evaluationReport}`,
        name: 'ConsensusEvaluator',
      }),
    ];

    // 更新研究状态
    const updatedResearch = {
      ...research,
      consensus: {
        summary,
        report: evaluationReport,
        consensusScores,
        probabilityWeights,
        keyVariables,
        investmentRecommendation,
        riskRewardRatio,
        evaluator: 'ConsensusEvaluator',
        timestamp: new Date().toISOString(),
        // 添加数据库需要的字段
        bull_strength: consensusScores.bullStrength || null,
        bear_strength: consensusScores.bearStrength || null,
        consensus_direction: determinConsensusDirection(consensusScores, probabilityWeights),
        consensus_confidence: investmentRecommendation.confidence || null,
        synthesis_summary: summary,
        key_agreement_points: extractAgreementPoints(evaluationReport),
        key_disagreement_points: extractDisagreementPoints(evaluationReport),
      },
    };

    console.log(`[共识评估师] 共识评估完成，投资建议: ${investmentRecommendation.action}`);
    return {
      messages: newMessages,
      research: updatedResearch,
      currentStage: 'consensus_evaluation_completed',
      progress: Math.min(state.progress + 15, 100),
    };
  } catch (error) {
    console.error('[共识评估师] 评估失败:', error);
    const errorMessage = `共识评估失败: ${error instanceof Error ? error.message : String(error)}`;

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【共识评估师】${errorMessage}`,
        name: 'ConsensusEvaluator',
      }),
    ];

    const updatedResearch = {
      ...state.research,
      consensus: {
        summary: '共识评估失败',
        report: errorMessage,
        error: true,
        evaluator: 'ConsensusEvaluator',
        timestamp: new Date().toISOString(),
      },
    };

    return { messages: newMessages, research: updatedResearch };
  }
}

// 辅助函数：格式化辩论历史
function formatDebateHistory(debateRounds: any[]): string {
  if (!debateRounds || debateRounds.length === 0) {
    return '无辩论历史记录';
  }

  let history = '';
  debateRounds.forEach((round, index) => {
    history += `\n【第${round.round}轮辩论】\n`;
    history += `焦点问题: ${round.focusPoints?.join(', ') || '无'}\n`;
    history += `关键问题: ${round.keyQuestions?.join(', ') || '无'}\n`;
    history += `轮次总结: ${round.summary || '无总结'}\n`;
    history += `质量评分: ${round.quality ? (round.quality * 100).toFixed(1) : 'N/A'}%\n`;
  });

  return history;
}

// 辅助函数：格式化最终观点
function formatFinalView(researchData: any): string {
  if (!researchData) return '观点不可用';

  let view = `【核心观点】\n${researchData.summary}\n\n`;

  if (researchData.keyArguments && researchData.keyArguments.length > 0) {
    view += `【关键论点】\n`;
    researchData.keyArguments.forEach((arg: string, index: number) => {
      view += `${index + 1}. ${arg}\n`;
    });
    view += '\n';
  }

  if (researchData.targetPrice) {
    view += `【目标价位】${researchData.targetPrice}\n`;
  }

  if (researchData.confidence) {
    view += `【置信度】${(researchData.confidence * 100).toFixed(1)}%\n`;
  }

  return view;
}

// 辅助函数：提取共识评分
function extractConsensusScores(report: string): Record<string, number> {
  const scores: Record<string, number> = {};

  const scorePatterns = {
    bullStrength: /多头论证强度[：:]\s*([0-9.]+)/,
    bearStrength: /空头论证强度[：:]\s*([0-9.]+)/,
    upwardPotential: /上行潜力[：:]\s*([0-9.]+)/,
    downwardRisk: /下行风险[：:]\s*([0-9.]+)/,
    certainty: /投资确定性[：:]\s*([0-9.]+)/,
    timing: /时机适宜性[：:]\s*([0-9.]+)/,
  };

  for (const [key, pattern] of Object.entries(scorePatterns)) {
    const match = report.match(pattern);
    if (match) {
      scores[key] = parseFloat(match[1]);
    }
  }

  return scores;
}

// 辅助函数：提取概率权重
function extractProbabilityWeights(report: string): Record<string, number> {
  const weights: Record<string, number> = {};

  const weightPatterns = {
    bullish: /多头[情景|概率][：:]\s*([0-9.]+)%?/,
    bearish: /空头[情景|概率][：:]\s*([0-9.]+)%?/,
    neutral: /中性[情景|概率][：:]\s*([0-9.]+)%?/,
  };

  for (const [key, pattern] of Object.entries(weightPatterns)) {
    const match = report.match(pattern);
    if (match) {
      weights[key] = parseFloat(match[1]) / 100; // 转换为小数
    }
  }

  // 如果没有提取到权重，使用默认值
  if (Object.keys(weights).length === 0) {
    weights.bullish = 0.4;
    weights.neutral = 0.3;
    weights.bearish = 0.3;
  }

  return weights;
}

// 辅助函数：提取关键变量
function extractKeyVariables(report: string): string[] {
  const variables: string[] = [];

  const variablePatterns = [/关键变量|核心因素/g, /监控[指标|变量]/g, /重要[因素|变量]/g];

  const lines = report.split('\n');
  for (const line of lines) {
    for (const pattern of variablePatterns) {
      if (pattern.test(line) && line.length > 10 && line.length < 150) {
        variables.push(line.trim());
        break;
      }
    }
  }

  return variables.slice(0, 5); // 返回前5个关键变量
}

// 辅助函数：提取投资建议
function extractInvestmentRecommendation(report: string): {
  action: string;
  confidence: number;
  reasoning: string;
} {
  let action = '观望';
  let confidence = 0.5;
  let reasoning = '基于综合分析的建议';

  // 提取行动建议
  if (/强烈[买入|推荐]/.test(report)) action = '强烈买入';
  else if (/买入|做多/.test(report)) action = '买入';
  else if (/卖出|做空/.test(report)) action = '卖出';
  else if (/减持/.test(report)) action = '减持';
  else if (/持有/.test(report)) action = '持有';

  // 提取置信度
  const confidenceMatch = report.match(/建议置信度[：:]\s*([0-9.]+)/);
  if (confidenceMatch) {
    confidence = parseFloat(confidenceMatch[1]);
    if (confidence > 1) confidence = confidence / 100; // 如果是百分比形式
  }

  // 提取推理
  const reasoningMatch = report.match(/建议理由[：:](.{50,200})/);
  if (reasoningMatch) {
    reasoning = reasoningMatch[1].trim();
  }

  return { action, confidence, reasoning };
}

// 辅助函数：计算风险收益比
function calculateRiskRewardRatio(
  scores: Record<string, number>,
  weights: Record<string, number>
): number {
  const upwardPotential = scores.upwardPotential || 5;
  const downwardRisk = scores.downwardRisk || 5;
  const bullishWeight = weights.bullish || 0.4;
  const bearishWeight = weights.bearish || 0.3;

  // 加权风险收益比计算
  const weightedReward = upwardPotential * bullishWeight;
  const weightedRisk = downwardRisk * bearishWeight;

  return weightedRisk > 0 ? weightedReward / weightedRisk : 1.0;
}

// 辅助函数：生成共识摘要
function generateConsensusSummary(
  report: string,
  recommendation: { action: string; confidence: number; reasoning: string },
  scores: Record<string, number>,
  weights: Record<string, number>
): string {
  const lines = report.split('\n').filter((line) => line.trim());
  const firstParagraph = lines.slice(0, 2).join(' ');

  let summary = `共识评估 - ${recommendation.action} (置信度: ${(
    recommendation.confidence * 100
  ).toFixed(1)}%)`;
  summary += ` - ${firstParagraph.substring(0, 100)}...`;

  if (scores.bullStrength && scores.bearStrength) {
    summary += ` 多空强度: ${scores.bullStrength.toFixed(1)}/${scores.bearStrength.toFixed(1)}`;
  }

  if (weights.bullish) {
    summary += ` 多头概率: ${(weights.bullish * 100).toFixed(0)}%`;
  }

  return summary;
}

// 辅助函数：确定共识方向
function determinConsensusDirection(
  scores: Record<string, number>,
  weights: Record<string, number>
): 'bullish' | 'bearish' | 'neutral' {
  const bullStrength = scores.bullStrength || 5;
  const bearStrength = scores.bearStrength || 5;
  const bullWeight = weights.bullish || 0.33;
  const bearWeight = weights.bearish || 0.33;

  const bullScore = bullStrength * bullWeight;
  const bearScore = bearStrength * bearWeight;

  if (bullScore > bearScore * 1.2) return 'bullish';
  if (bearScore > bullScore * 1.2) return 'bearish';
  return 'neutral';
}

// 辅助函数：提取共识点
function extractAgreementPoints(report: string): any {
  const agreementPoints: string[] = [];
  const lines = report.split('\n');

  let inAgreementSection = false;
  for (const line of lines) {
    if (/共识|一致|认同|同意/.test(line) && line.length > 10) {
      inAgreementSection = true;
      agreementPoints.push(line.trim());
    } else if (inAgreementSection && line.trim().startsWith('-')) {
      agreementPoints.push(line.trim());
    } else if (inAgreementSection && line.trim() === '') {
      inAgreementSection = false;
    }
  }

  return agreementPoints.length > 0 ? agreementPoints : null;
}

// 辅助函数：提取分歧点
function extractDisagreementPoints(report: string): any {
  const disagreementPoints: string[] = [];
  const lines = report.split('\n');

  let inDisagreementSection = false;
  for (const line of lines) {
    if (/分歧|争议|不同|差异/.test(line) && line.length > 10) {
      inDisagreementSection = true;
      disagreementPoints.push(line.trim());
    } else if (inDisagreementSection && line.trim().startsWith('-')) {
      disagreementPoints.push(line.trim());
    } else if (inDisagreementSection && line.trim() === '') {
      inDisagreementSection = false;
    }
  }

  return disagreementPoints.length > 0 ? disagreementPoints : null;
}
