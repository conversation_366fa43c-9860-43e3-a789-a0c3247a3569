// 服务端专用的 LangGraph 模块
// 这个文件只在 Node.js 环境中运行

import { BaseMessage, HumanMessage, isAIMessage } from '@langchain/core/messages';
import { MemorySaver } from '@langchain/langgraph';
import { EventEmitter } from 'events';
import { omit } from 'lodash';
import './process-cleanup'; // 自动注册进程清理处理器
import {
  AnalystType,
  CreateWorkflowRequest,
  LogWorkflowEventRequest,
  SaveAnalystReportRequest,
  SaveConsensusEvaluationRequest,
  SaveFinalDecisionRequest,
  SaveResearchReportRequest,
  SaveRiskAssessmentRequest,
  UpdateWorkflowStatusRequest,
} from '../types/langgraph-database';
import { akshareAdapter } from './akshare/adapter';
import LangGraphDatabase from './langgraph-database';
import { createTradingWorkflow, TradingAgentAnnotation } from './langgraph-state';

// 定义状态接口
export interface TradingAgentState {
  messages: BaseMessage[];
  ticker?: string;
  analysisConfig?: any;
  analysisResults?: any;
  tradingDecision?: any;
  riskAssessment?: any;
}

// 定义会话状态接口
export interface SessionState {
  workflowId: string; // Renamed from threadId for clarity
  messages: BaseMessage[];
  currentStep: string;
  isProcessing: boolean;
  analysisResults?: any;
  tradingDecision?: any;
  error?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// LangGraph 服务类
export class LangGraphService extends EventEmitter {
  private workflow: any;
  private memory: MemorySaver;
  private sessions: Map<string, SessionState> = new Map();
  private isInitialized = false;

  constructor() {
    super();
    // 设置最大监听器数量，避免内存泄漏警告
    this.setMaxListeners(50);
    this.memory = new MemorySaver();
    this.workflow = createTradingWorkflow();
    this.initialize();
  }

  // 初始化服务，启动依赖项
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }
    console.log('Initializing LangGraphService...');
    try {
      await akshareAdapter.start();
      this.isInitialized = true;
      console.log('LangGraphService initialized successfully.');
    } catch (error) {
      console.error('Failed to initialize LangGraphService:', error);
      // 在构建时不退出进程，只记录错误
      if (typeof window === 'undefined' && process.env.NODE_ENV === 'production') {
        console.warn('构建时初始化失败，这是正常的');
        this.isInitialized = true; // 标记为已初始化，避免重复尝试
        return;
      }
      // 在运行时，可以选择是否退出进程
      // process.exit(1); // 如果关键依赖无法启动，则退出
    }
  }

  // 创建新会话
  public async createSession(request: Omit<CreateWorkflowRequest, 'workflow_id'>): Promise<string> {
    console.log('[LangGraphService] Creating new session with request:', request);
    const workflowId = await LangGraphDatabase.createWorkflow(request);
    console.log(`[LangGraphService] New workflow created in DB with ID: ${workflowId}`);

    const session: SessionState = {
      workflowId: workflowId,
      messages: [],
      currentStep: 'created',
      isProcessing: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.sessions.set(workflowId, session);
    console.log(`[LangGraphService] In-memory session created for workflow ID: ${workflowId}`);
    return workflowId;
  }

  // 获取会话状态
  public getSession(workflowId: string): SessionState | null {
    return this.sessions.get(workflowId) || null;
  }

  // 从数据库工作流恢复会话到内存
  private async restoreSessionFromWorkflow(workflow: any): Promise<SessionState> {
    console.log(`[LangGraphService] Restoring session from workflow: ${workflow.workflow_id}`);
    
    const session: SessionState = {
      workflowId: workflow.workflow_id,
      messages: [], // 可以从数据库的 workflow_events 表恢复消息
      currentStep: workflow.current_stage || 'restored',
      isProcessing: workflow.status === 'running',
      createdAt: new Date(workflow.created_at),
      updatedAt: new Date(workflow.updated_at),
      error: workflow.error_message || null,
    };

    // 将恢复的会话存储到内存中
    this.sessions.set(workflow.workflow_id, session);
    
    console.log(
      `[LangGraphService] Session restored for workflow ${workflow.workflow_id}. Status: ${workflow.status}, Stage: ${workflow.current_stage}`
    );
    
    return session;
  }

  // 更新会话状态
  private async updateSession(
    workflowId: string,
    updates: Partial<SessionState>,
    dbUpdates?: Omit<UpdateWorkflowStatusRequest, 'workflow_id'>
  ): Promise<void> {
    const session = this.sessions.get(workflowId);
    if (session) {
      const oldStep = session.currentStep;
      Object.assign(session, updates, { updatedAt: new Date() });
      this.sessions.set(workflowId, session);
      console.log(
        `[LangGraphService] Session ${workflowId} updated. Step: '${oldStep}' -> '${
          session.currentStep
        }', Status: ${session.isProcessing ? 'Processing' : 'Idle'}`
      );

      if (dbUpdates) {
        const request: UpdateWorkflowStatusRequest = {
          workflow_id: workflowId,
          current_stage: dbUpdates.current_stage || session.currentStep,
          progress: dbUpdates.progress || 0,
          status: dbUpdates.status || 'running',
          error_message: dbUpdates.error_message,
        };
        await LangGraphDatabase.updateWorkflowStatus(request);
        console.log(`[LangGraphService] DB status for workflow ${workflowId} updated:`, request);
      }

      // 发射状态更新事件
      this.emit('sessionUpdate', { workflowId, session });
    } else {
      console.warn(`[LangGraphService] Attempted to update non-existent session: ${workflowId}`);
    }
  }

  // 发送消息
  public async sendMessage(workflowId: string, message: string): Promise<any> {
    let session = this.getSession(workflowId);
    if (!session) {
      // This case should ideally be handled by creating a workflow first.
      // For now, we'll throw an error.
      throw new Error(`Session with workflowId ${workflowId} not found.`);
    }

    try {
      await this.updateSession(
        workflowId,
        { isProcessing: true, currentStep: '处理消息...', error: null },
        { status: 'running', progress: 5, current_stage: 'processing_message' }
      );

      // 添加用户消息
      const humanMessage = new HumanMessage(message);

      // 调用 workflow 执行
      const config = { configurable: { thread_id: workflowId } };
      const result = await this.workflow.invoke({ messages: [humanMessage] }, config);

      // 更新会话状态
      await this.updateSession(
        workflowId,
        { isProcessing: false, currentStep: '完成', messages: result.messages },
        { status: 'completed', progress: 100, current_stage: 'finished' }
      );

      // 将结果保存到数据库
      await this._saveGraphResultToDb(workflowId, result);

      return {
        content: result.messages[result.messages.length - 1].content,
        metadata: {
          workflowId,
          timestamp: new Date().toISOString(),
          messageType: 'chat_response',
        },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';

      await this.updateSession(
        workflowId,
        { isProcessing: false, currentStep: '处理失败', error: errorMessage },
        {
          status: 'failed',
          error_message: errorMessage,
          progress: session.isProcessing ? 50 : 100, // Provide default
          current_stage: 'error', // Provide default
        }
      );

      throw error;
    }
  }

  // 分析股票
  public async analyzeStock(workflowId: string, ticker: string, config: any = {}): Promise<any> {
    console.log(
      `[LangGraphService] Starting stock analysis for workflow: ${workflowId}, ticker: ${ticker}`
    );
    let session = this.getSession(workflowId);
    
    if (!session) {
      console.log(
        `[LangGraphService] No existing session found for ${workflowId}. Checking database...`
      );
      
      // 先检查数据库中是否存在该工作流
      const existingWorkflow = await LangGraphDatabase.getWorkflow(workflowId);
      
      if (existingWorkflow) {
        console.log(
          `[LangGraphService] Found existing workflow in database: ${workflowId}. Restoring session...`
        );
        // 从数据库恢复 session 到内存
        session = await this.restoreSessionFromWorkflow(existingWorkflow);
      } else {
        console.log(
          `[LangGraphService] No existing workflow found for ${workflowId}. Creating a new one.`
        );
        // 创建新的工作流
        workflowId = await this.createSession({
          ticker: ticker || '',
          title: `Analysis for ${ticker || 'Unknown'}`,
          description: `Stock analysis workflow for ${ticker || 'Unknown'}`,
          config: config || {},
          created_by: 'system',
        });
        session = this.getSession(workflowId)!;
      }
    }

    try {
      await this.updateSession(
        workflowId,
        { isProcessing: true, currentStep: '开始分析...', error: null },
        { status: 'running', progress: 10, current_stage: 'starting_analysis' }
      );

      // 创建分析消息
      const analysisMessage = new HumanMessage(`请分析股票 ${ticker}`);
      console.log(`[LangGraphService] Invoking workflow for thread_id: ${workflowId}`);

      // 调用 workflow 执行
      const graphConfig = { configurable: { thread_id: workflowId } };
      const result = await this.workflow.invoke(
        {
          messages: [analysisMessage],
          ticker,
          config: config,
        },
        graphConfig
      );
      console.log('[LangGraphService] Workflow invocation completed. Raw result:', result);

      // 更新会话状态
      await this.updateSession(
        workflowId,
        {
          isProcessing: false,
          currentStep: '分析完成',
          messages: result.messages,
          analysisResults: result.analysisResults,
          tradingDecision: result.tradingDecision,
        },
        { status: 'completed', progress: 100, current_stage: 'finished' }
      );

      // 将结果写入文件（用于调试）
      await this._writeResultToFile(workflowId, result);

      // 将结果保存到数据库
      await this._saveGraphResultToDb(workflowId, result);

      console.log(`[LangGraphService] Analysis for workflow ${workflowId} finished successfully.`);
      return {
        content: result.messages[result.messages.length - 1].content,
        analysisResults: result.analysis,
        tradingDecision: result.decision,
        metadata: {
          workflowId,
          ticker,
          timestamp: new Date().toISOString(),
          messageType: 'analysis_response',
        },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error(`[LangGraphService] Analysis failed for workflow ${workflowId}:`, error);

      await this.updateSession(
        workflowId,
        { isProcessing: false, currentStep: '分析失败', error: errorMessage },
        {
          status: 'failed',
          error_message: errorMessage,
          progress: session?.isProcessing ? 50 : 100, // Provide default
          current_stage: 'error', // Provide default
        }
      );

      throw error;
    }
  }

  // 流式分析
  public async *streamAnalysis(
    threadId: string,
    ticker: string,
    config: any = {}
  ): AsyncGenerator<any> {
    let session = this.getSession(threadId);
    
    if (!session) {
      console.log(
        `[LangGraphService] No existing session found for ${threadId}. Checking database...`
      );
      
      // 先检查数据库中是否存在该工作流
      const existingWorkflow = await LangGraphDatabase.getWorkflow(threadId);
      
      if (existingWorkflow) {
        console.log(
          `[LangGraphService] Found existing workflow in database: ${threadId}. Restoring session...`
        );
        // 从数据库恢复 session 到内存
        session = await this.restoreSessionFromWorkflow(existingWorkflow);
      } else {
        console.log(
          `[LangGraphService] No existing workflow found for ${threadId}. Creating a new one.`
        );
        // 创建新的工作流
        const newWorkflowId = await this.createSession({
          ticker,
          title: `Streamed Analysis for ${ticker}`,
          description: `Streamed stock analysis workflow for ${ticker}`,
          config,
          created_by: 'system',
        });
        session = this.getSession(newWorkflowId)!;
        threadId = newWorkflowId; // Update threadId to the new workflowId
      }
    }

    try {
      // 开始分析
      this.updateSession(threadId, {
        isProcessing: true,
        currentStep: '初始化分析...',
        error: null,
      });

      yield { currentStep: '初始化分析...', progress: 0 };

      // 模拟分析步骤
      const steps = [
        { step: '收集数据...', progress: 20 },
        { step: '基本面分析...', progress: 40 },
        { step: '技术分析...', progress: 60 },
        { step: '情绪分析...', progress: 80 },
        { step: '生成决策...', progress: 100 },
      ];

      for (const stepInfo of steps) {
        this.updateSession(threadId, { currentStep: stepInfo.step });
        yield stepInfo;
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      // 执行最终分析
      const result = await this.analyzeStock(threadId, ticker, config);
      yield { ...result, progress: 100 };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.updateSession(threadId, {
        isProcessing: false,
        currentStep: '分析失败',
        error: errorMessage,
      });
      throw error;
    }
  }

  // 清除会话
  public clearSession(threadId: string): void {
    this.sessions.delete(threadId);
    this.emit('sessionCleared', { threadId });
  }

  // 获取所有活跃会话
  public getActiveSessions(): SessionState[] {
    return Array.from(this.sessions.values());
  }

  // 将结果写入文件（用于调试和日志记录）
  private async _writeResultToFile(threadId: string, result: any): Promise<void> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');

      // 创建结果目录
      const resultsDir = path.join(process.cwd(), 'results');
      try {
        await fs.mkdir(resultsDir, { recursive: true });
      } catch (error) {
        // 目录可能已存在，忽略错误
      }

      // 写入结果文件
      const filename = `${threadId}_${Date.now()}.json`;
      const filepath = path.join(resultsDir, filename);

      const resultData = {
        threadId,
        timestamp: new Date().toISOString(),
        messages: result.messages,
        analysis: result.analysis,
        decision: result.decision,
        risk: result.risk,
        research: result.research,
      };

      await fs.writeFile(filepath, JSON.stringify(resultData, null, 2), 'utf-8');
      console.log(`[LangGraphService] Result written to file: ${filepath}`);
    } catch (error) {
      console.error(`[LangGraphService] Failed to write result to file:`, error);
      // 不抛出错误，因为文件写入失败不应该影响主要功能
    }
  }

  // 将 LangGraph 状态保存到数据库 (Refactored for new schema)
  private async _saveGraphResultToDb(
    workflowId: string,
    result: typeof TradingAgentAnnotation.State
  ): Promise<void> {
    console.log(`[LangGraphService] Saving graph result to DB for workflow: ${workflowId}`);
    try {
      // 1. Log Messages as Events
      if (result.messages && result.messages.length > 0) {
        console.log(`[LangGraphService] Saving ${result.messages.length} messages as events.`);
        for (const message of result.messages) {
          const event: LogWorkflowEventRequest = {
            workflow_id: workflowId,
            event_type: 'message',
            content: message.content.toString(),
            metadata: {
              sender: isAIMessage(message) ? 'ai' : 'human',
              ...omit(message, ['content']),
            },
          };
          await LangGraphDatabase.logWorkflowEvent(event);
        }
      }

      // 2. Save Analyst Reports
      if (result.analysis && Object.keys(result.analysis).length > 0) {
        console.log(
          `[LangGraphService] Saving ${Object.keys(result.analysis).length} analyst reports.`
        );
        for (const [analystType, report] of Object.entries(result.analysis)) {
          // Type assertion to fix compiler error
          const typedReport = report as { summary?: string; [key: string]: any };
          const request: SaveAnalystReportRequest = {
            workflow_id: workflowId,
            analyst_type: analystType as AnalystType,
            summary: typedReport.summary,
            details: omit(typedReport, ['summary']),
          };
          await LangGraphDatabase.saveAnalystReport(request);
        }
      }

      // 3. Save Research Reports (Bull/Bear)
      if (result.research) {
        console.log('[LangGraphService] Saving research reports.');
        if (result.research.bull) {
          const bullRequest: SaveResearchReportRequest = {
            workflow_id: workflowId,
            researcher_type: 'bull',
            summary: result.research.bull.summary,
            arguments: result.research.bull.arguments || [],
          };
          await LangGraphDatabase.saveResearchReport(bullRequest);
        }
        if (result.research.bear) {
          const bearRequest: SaveResearchReportRequest = {
            workflow_id: workflowId,
            researcher_type: 'bear',
            summary: result.research.bear.summary,
            arguments: result.research.bear.arguments || [],
          };
          await LangGraphDatabase.saveResearchReport(bearRequest);
        }
      }

      // 4. Save Consensus Evaluation
      if (result.research && result.research.consensus) {
        console.log('[LangGraphService] Saving consensus evaluation.');
        const consensusData = result.research.consensus as Omit<
          SaveConsensusEvaluationRequest,
          'workflow_id'
        >;
        const consensusRequest: SaveConsensusEvaluationRequest = {
          workflow_id: workflowId,
          bull_strength: consensusData.bull_strength,
          bear_strength: consensusData.bear_strength,
          consensus_direction: consensusData.consensus_direction,
          consensus_confidence: consensusData.consensus_confidence,
          synthesis_summary: consensusData.synthesis_summary,
          key_agreement_points: consensusData.key_agreement_points,
          key_disagreement_points: consensusData.key_disagreement_points,
        };
        await LangGraphDatabase.saveConsensusEvaluation(consensusRequest);
      }

      // 5. Save Risk Assessment
      if (result.risk) {
        console.log('[LangGraphService] Saving risk assessment.');
        const riskData = result.risk as Omit<SaveRiskAssessmentRequest, 'workflow_id'>;
        const riskRequest: SaveRiskAssessmentRequest = {
          workflow_id: workflowId,
          overall_risk_level: riskData.overall_risk_level,
          risk_score: riskData.risk_score,
          summary: riskData.summary,
          market_risk: riskData.market_risk,
          liquidity_risk: riskData.liquidity_risk,
          credit_risk: riskData.credit_risk,
          operational_risk: riskData.operational_risk,
          scenario_analysis: riskData.scenario_analysis,
          risk_metrics: riskData.risk_metrics,
          recommendations: riskData.recommendations,
          risk_controls: riskData.risk_controls,
          risk_warnings: riskData.risk_warnings,
          status: riskData.status || 'completed',
          execution_time_ms: riskData.execution_time_ms,
        };
        await LangGraphDatabase.saveRiskAssessment(riskRequest);
      }

      // 6. Save Final Decision
      if (result.decision) {
        console.log('[LangGraphService] Saving final decision.');
        const decisionData = result.decision as Omit<SaveFinalDecisionRequest, 'workflow_id'>;
        const decisionRequest: SaveFinalDecisionRequest = {
          workflow_id: workflowId,
          decision_type: decisionData.decision_type,
          confidence_level: decisionData.confidence_level,
          decision_rationale: decisionData.decision_rationale,
          entry_price_range: decisionData.entry_price_range,
          stop_loss_price: decisionData.stop_loss_price,
          take_profit_price: decisionData.take_profit_price,
          position_size_percentage: decisionData.position_size_percentage,
        };
        await LangGraphDatabase.saveFinalDecision(decisionRequest);
      }

      // ... Add saving logic for debate sessions, risk assessments etc. as needed ...

      console.log(`[LangGraphService] Successfully saved graph result for workflow: ${workflowId}`);
    } catch (dbError) {
      console.error(
        `[LangGraphService] Failed to save graph result for workflow ${workflowId}:`,
        dbError
      );
      // Log error to the new events table
      await LangGraphDatabase.logWorkflowEvent({
        workflow_id: workflowId,
        event_type: 'error',
        content: `Failed to save graph result: ${
          dbError instanceof Error ? dbError.message : String(dbError)
        }`,
        metadata: { error: dbError },
      });
    }
  }

  public  cleanSession(){
     this.sessions.clear()
  }
}

// 创建全局服务实例 (Singleton)
let langGraphServiceInstance: LangGraphService | null = null;

export const getLangGraphService = (): LangGraphService => {
  if (!langGraphServiceInstance) {
    langGraphServiceInstance = new LangGraphService();
  }
  return langGraphServiceInstance;
};

export const langGraphService = getLangGraphService();

// 全局清理函数
export const cleanupLangGraphService = (): void => {
  if (langGraphServiceInstance) {
    // 清理所有监听器
    langGraphServiceInstance.removeAllListeners();
    // 清理会话数据
    langGraphServiceInstance.cleanSession();
    langGraphServiceInstance = null;
  }
};
