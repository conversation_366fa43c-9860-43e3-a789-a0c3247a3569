import { Task } from '@/types/database';
import axios from 'axios';

// API 基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

// 创建 axios 实例
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // 重要：允许发送和接收 cookies
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从 userStore 获取认证头
    if (typeof window !== 'undefined') {
      // 动态导入 userStore 以避免 SSR 问题
      import('@/store/userStore').then(({ default: useUserStore }) => {
        const authHeaders = useUserStore.getState().getAuthHeaders();
        Object.assign(config.headers, authHeaders);
      });

      // 备用方案：直接从 localStorage 获取
      const sessionId = localStorage.getItem('sessionId');
      if (sessionId) {
        config.headers['X-Session-ID'] = sessionId;
      }
    }
    config.withCredentials = true;

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 统一API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 如果响应数据已经是 ApiResponse 格式，直接返回
    if (response.data && typeof response.data.success === 'boolean') {
      return response.data;
    }
    // 否则，包装成 ApiResponse 格式
    return {
      success: true,
      data: response.data,
      message: 'Request successful',
    };
  },
  (error) => {
    console.error('API Error:', error);

    // // 处理401未授权错误
    // if (error.response?.status === 401) {
    //   // 清除本地存储的用户信息
    //   if (typeof window !== 'undefined') {
    //     localStorage.removeItem('currentUser');
    //     localStorage.removeItem('sessionId');
    //     // 重定向到登录页面
    //     window.location.href = '/login';
    //   }
    // }
    const response: ApiResponse = {
      success: false,
      data: null,
      message: error.response?.data?.message || error.message || 'An unknown error occurred',
      code: error.response?.status,
    };
    return Promise.resolve(response); // 返回一个 resolved promise，让业务代码处理
  }
);

// 分析配置接口
export interface AnalysisConfig {
  ticker: string;
  analysisDate: string;
  selectedAnalysts: string[];
  llmProvider: string;
  deepThinkLlm: string;
  quickThinkLlm: string;
  maxDebateRounds: number;
  maxRiskDiscussRounds: number;
  onlineTools: boolean;
  researchDepth: string;
}

// 分析状态接口
export interface AnalysisState {
  currentStage: string;
  progress: number;
  isComplete: boolean;
  error?: string;
}

// 代理状态接口
export interface AgentStatus {
  id: string;
  name: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  progress: number;
  lastUpdate: string;
  message?: string;
}

// 分析报告接口
export interface AnalysisReport {
  type: string;
  title: string;
  content: string;
  timestamp: string;
  agent: string;
}

// 交易决策接口
export interface TradingDecision {
  action: 'buy' | 'sell' | 'hold';
  confidence: number;
  reasoning: string;
  riskLevel: 'low' | 'medium' | 'high';
  targetPrice?: number;
  stopLoss?: number;
  positionSize?: number;
  timeHorizon?: string;
  timestamp: string;
}

// 认证相关接口
export interface User {
  id: number;
  email: string;
  username: string;
  email_verified: boolean;
  avatar_url?: string;
  role: string;
  created_at: string;
  last_login_at?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  sessionId: string;
}

// 认证API方法
export const authApi = {
  // 用户登录
  login: (credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> => {
    return api.post('/api/auth/login', credentials);
  },

  // 用户注册
  register: (userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> => {
    return api.post('/api/auth/register', userData);
  },

  // 用户登出
  logout: (): Promise<ApiResponse<{ message: string }>> => {
    return api.post('/api/auth/logout');
  },
};

// 获取当前用户信息
export const getCurrentUser = (): Promise<ApiResponse<User>> => {
  return api.get('/api/auth/me');
};

// 消息数据类型定义
export interface Message {
  id: number;
  message_id: string;
  task_id: string;
  message_type: 'human' | 'ai' | 'system' | 'tool';
  content: string;
  metadata: any;
  sequence_number: number;
  parent_message_id: string | null;
  created_at: string;
}

// 分页信息
export interface Pagination {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

// API响应类型
export interface MessagesResponse {
  messages: Message[];
  pagination: Pagination;
}

// 消息相关API方法
export const messageApi = {
  // 获取消息列表
  getMessages: (params: {
    taskId?: string;
    conversationId?: string;
    limit?: number;
    offset?: number;
    messageType?: string;
  }): Promise<ApiResponse<MessagesResponse>> => {
    return api.get('/api/database/messages', { params });
  },
};

// 任务相关API方法 (兼容工作流)
export const taskApi = {
  // 获取任务列表 (实际查询工作流)
  getTasks: (): Promise<ApiResponse<Task[]>> => {
    return api.get('/api/database/tasks');
  },

  // 更新任务状态 (实际更新工作流)
  updateTaskStatus: (taskId: string, status: string): Promise<ApiResponse<void>> => {
    return api.patch(`/api/database/tasks/${taskId}/status`, { status });
  },

  // 启动任务分析
  startAnalysis: (taskData: any): Promise<ApiResponse<any>> => {
    return api.post('/api/langgraph/analyze', taskData);
  },
};

// API 方法
export const tradingApi = {
  // 开始分析
  startAnalysis: (config: AnalysisConfig): Promise<ApiResponse<{ analysisId: string }>> => {
    return api.post('/api/langgraph/analysis/start', config);
  },

  // 获取分析状态
  getAnalysisStatus: (analysisId: string): Promise<ApiResponse<AnalysisState>> => {
    return api.get(`/api/langgraph/analysis/status?analysisId=${analysisId}`);
  },

  // 获取代理状态
  getAgentStatuses: (analysisId: string): Promise<ApiResponse<AgentStatus[]>> => {
    return api.get(`/api/langgraph/analysis/agents?analysisId=${analysisId}`);
  },

  // 获取分析报告
  getReports: (analysisId: string): Promise<ApiResponse<AnalysisReport[]>> => {
    return api.get(`/api/langgraph/analysis/reports?analysisId=${analysisId}`);
  },

  // 获取交易决策
  getTradingDecision: (analysisId: string): Promise<ApiResponse<TradingDecision | null>> => {
    return api.get(`/api/langgraph/analysis/decision?analysisId=${analysisId}`);
  },

  // 停止分析
  stopAnalysis: (analysisId: string): Promise<ApiResponse<void>> => {
    return api.delete(`/api/langgraph/analysis/status?analysisId=${analysisId}`);
  },

  // 获取股票实时数据
  getStockData: (ticker: string, date: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/data/stock/${ticker}`, {
      params: { date },
    });
  },

  // 获取新闻数据
  getNewsData: (ticker: string, date: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/data/news/${ticker}`, {
      params: { date },
    });
  },

  // 获取技术指标数据
  getTechnicalIndicators: (ticker: string, date: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/data/technical/${ticker}`, {
      params: { date },
    });
  },

  // 获取基本面数据
  getFundamentalsData: (ticker: string, date: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/data/fundamentals/${ticker}`, {
      params: { date },
    });
  },

  // 健康检查
  healthCheck: (): Promise<ApiResponse<{ status: string; version: string }>> => {
    return api.get('/api/health');
  },
};

// LangGraph 工作流相关接口
export interface WorkflowStatus {
  workflow_id: string;
  task_id: string;
  ticker: string;
  current_stage: string;
  workflow_status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  workflow_created_at: string;
  workflow_completed_at?: string;

  // 完成状态
  fundamental_completed: boolean;
  technical_completed: boolean;
  sentiment_completed: boolean;
  news_completed: boolean;
  bull_completed: boolean;
  bear_completed: boolean;
  consensus_completed: boolean;
  risk_assessment_completed: boolean;
  decision_completed: boolean;

  // 统计信息
  total_debate_messages: number;
  max_debate_round: number;
  total_workflow_messages: number;
  duration_seconds: number;
}

export interface WorkflowDetails {
  workflow: any;
  status: WorkflowStatus;
  analysts: {
    fundamental?: any;
    technical?: any;
    sentiment?: any;
    news?: any;
  };
  researchers: {
    bull?: any;
    bear?: any;
  };
  debate: {
    records: any[];
    rounds: number;
    totalArguments: number;
  };
  consensus?: any;
  risk?: any;
  decision?: any;
  messages: any[];
  snapshots: any[];
  statistics: {
    totalMessages: number;
    totalDebateRounds: number;
    durationSeconds: number;
    completionRate: {
      [key: string]: boolean;
    };
  };
}

export interface CreateWorkflowRequest {
  workflow_id: string;
  task_id: string;
  ticker: string;
  config?: any;
}

export interface UpdateWorkflowStageRequest {
  workflow_id: string;
  current_stage: string;
  progress: number;
  status: WorkflowStatus['workflow_status'];
}

export interface QueryWorkflowsOptions {
  task_id?: string;
  ticker?: string;
  status?: string;
  limit?: number;
  offset?: number;
}

// LangGraph 工作流 API 方法
export const langGraphApi = {
  // 创建工作流
  createWorkflow: (data: CreateWorkflowRequest): Promise<ApiResponse<{ workflow_id: string }>> => {
    return api.post('/api/langgraph/workflow', data);
  },

  // 更新工作流状态
  updateWorkflowStage: (
    data: UpdateWorkflowStageRequest
  ): Promise<ApiResponse<{ workflow_id: string }>> => {
    return api.put('/api/langgraph/workflow', data);
  },

  // 获取工作流状态
  getWorkflowStatus: (workflowId?: string): Promise<ApiResponse<WorkflowDetails>> => {
    const params: Record<string, string> = {};
    if (workflowId) params.workflow_id = workflowId;

    return api.get('/api/langgraph/workflow/status', { params });
  },

  // 查询工作流列表
  queryWorkflows: (options: QueryWorkflowsOptions = {}): Promise<ApiResponse<any[]>> => {
    return api.get('/api/langgraph/workflow', { params: options });
  },

  // 获取工作流详细信息（包含所有相关数据）
  getWorkflowDetails: (workflowId: string): Promise<ApiResponse<WorkflowDetails>> => {
    return api.get(`/api/langgraph/workflow/${workflowId}/details`);
  },

  // 获取分析师结果
  getAnalystResults: (workflowId: string, analystType?: string): Promise<ApiResponse<any[]>> => {
    const params = analystType ? { analyst_type: analystType } : {};
    return api.get(`/api/langgraph/workflow/${workflowId}/analysts`, { params });
  },

  // 获取研究员结果
  getResearcherResults: (
    workflowId: string,
    researcherType?: string
  ): Promise<ApiResponse<any[]>> => {
    const params = researcherType ? { researcher_type: researcherType } : {};
    return api.get(`/api/langgraph/workflow/${workflowId}/researchers`, { params });
  },

  // 获取辩论记录
  getDebateRecords: (workflowId: string, round?: number): Promise<ApiResponse<any[]>> => {
    const params = round ? { round: round.toString() } : {};
    return api.get(`/api/langgraph/workflow/${workflowId}/debate`, { params });
  },

  // 获取共识评估
  getConsensusEvaluation: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/langgraph/workflow/${workflowId}/consensus`);
  },

  // 获取风险评估
  getRiskAssessment: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/langgraph/workflow/${workflowId}/risk`);
  },

  // 获取交易决策
  getTradingDecision: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/langgraph/workflow/${workflowId}/decision`);
  },

  // 获取工作流消息
  getWorkflowMessages: (workflowId: string, stageName?: string): Promise<ApiResponse<any[]>> => {
    const params = stageName ? { stage_name: stageName } : {};
    return api.get(`/api/langgraph/workflow/${workflowId}/messages`, { params });
  },

  // 获取状态快照
  getStateSnapshots: (workflowId: string, stageName?: string): Promise<ApiResponse<any[]>> => {
    const params = stageName ? { stage_name: stageName } : {};
    return api.get(`/api/langgraph/workflow/${workflowId}/snapshots`, { params });
  },
};

// WebSocket 连接管理
export class TradingWebSocket {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor(analysisId: string) {
    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';
    this.url = `${wsUrl}/ws/analysis/${analysisId}`;
  }

  connect(onMessage: (data: any) => void, onError?: (error: Event) => void): void {
    try {
      this.ws = new WebSocket(this.url);

      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          onMessage(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.attemptReconnect(onMessage, onError);
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        if (onError) {
          onError(error);
        }
      };
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      if (onError) {
        onError(error as Event);
      }
    }
  }

  private attemptReconnect(onMessage: (data: any) => void, onError?: (error: Event) => void): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(
        `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`
      );

      setTimeout(() => {
        this.connect(onMessage, onError);
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  send(data: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    } else {
      console.warn('WebSocket is not connected');
    }
  }
}
