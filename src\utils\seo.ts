/**
 * SEO 工具函数集合
 */

// 生成页面标题
export function generatePageTitle(pageTitle?: string, siteName = 'TradingAgents') {
  if (!pageTitle) return `${siteName} - AI智能股票分析平台`;
  return `${pageTitle} | ${siteName} - AI智能股票分析平台`;
}

// 生成页面描述
export function generatePageDescription(customDescription?: string) {
  return (
    customDescription ||
    '专业的AI驱动股票分析平台，基于多智能体大语言模型技术，提供多维度智能分析，为投资决策提供全方位的数据支撑和智能建议。'
  );
}

// 生成关键词
export function generateKeywords(pageKeywords: string[] = []) {
  const baseKeywords = [
    '股票分析',
    'AI投资',
    '智能投资',
    '股票推荐',
    '投资决策',
    '多智能体',
    '大语言模型',
    'LangGraph',
    '金融科技',
    'FinTech',
    '市场分析',
    '技术分析',
    '基本面分析',
    '新闻分析',
    '情绪分析',
    '风险管理',
    '投资策略',
    '股票预测',
    '量化投资',
    '智能投顾',
    'TradingAgents',
    '交易智能体',
    '金融AI',
    '投资AI',
    '股票AI',
  ];

  const combined = [...pageKeywords, ...baseKeywords];
  return Array.from(new Set(combined));
}

// 生成结构化数据 - 面包屑导航
export function generateBreadcrumbStructuredData(
  breadcrumbs: Array<{ name: string; url: string }>
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  };
}

// 生成结构化数据 - FAQ
export function generateFAQStructuredData(faqs: Array<{ question: string; answer: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
}

// 生成结构化数据 - 产品/服务
export function generateProductStructuredData({
  name,
  description,
  image,
  price = '免费',
  currency = 'CNY',
  availability = 'https://schema.org/InStock',
}: {
  name: string;
  description: string;
  image: string;
  price?: string;
  currency?: string;
  availability?: string;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: name,
    description: description,
    image: image,
    applicationCategory: 'FinanceApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: price === '免费' ? '0' : price,
      priceCurrency: currency,
      availability: availability,
    },
  };
}

// URL 优化工具
export function createSEOFriendlyUrl(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/--+/g, '-') // 多个连字符合并为一个
    .trim();
}

// 生成 Open Graph 图片 URL
export function generateOGImageUrl(title: string, description?: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://tradingagent.top';
  const params = new URLSearchParams({
    title,
    ...(description && { description }),
  });
  return `${baseUrl}/api/og?${params.toString()}`;
}

// 检查是否为生产环境
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

// 获取完整的 URL
export function getFullUrl(path: string = ''): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://tradingagent.top';
  return `${baseUrl}${path.startsWith('/') ? path : `/${path}`}`;
}
