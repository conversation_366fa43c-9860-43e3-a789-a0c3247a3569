# TradingAgents Frontend

<p align="center">
  <img src="https://img.shields.io/badge/Next.js-15.3-black?logo=next.js" alt="Next.js" />
  <img src="https://img.shields.io/badge/React-18.0-blue?logo=react" alt="React" />
  <img src="https://img.shields.io/badge/TypeScript-5.0-blue?logo=typescript" alt="TypeScript" />
  <img src="https://img.shields.io/badge/Tailwind-3.3-blue?logo=tailwindcss" alt="Tailwind CSS" />
  <img src="https://img.shields.io/badge/LangGraph-0.3-green?logo=langchain" alt="LangGraph" />
  <img src="https://img.shields.io/badge/MySQL-8.0-orange?logo=mysql" alt="MySQL" />
</p>

**TradingAgents 多智能体大语言模型金融交易框架的前端界面** - 一个基于 Next.js 构建的现代化、智能化的金融分析平台，模拟真实交易公司的专业分工和决策流程。

## 📖 项目概述

TradingAgents Frontend 是一个企业级的金融分析前端应用，专为多智能体交易分析框架设计。该项目采用现代化的技术栈，实现了完整的任务管理、实时分析、智能对话和数据可视化功能，为用户提供专业的股票分析和交易决策支持。

> **⚠️ 重要更新**: 项目已从 `tasks` 表迁移到 `workflows` 表，提供更好的工作流管理和状态跟踪。详见 [迁移指南](./TASKS_TO_WORKFLOWS_MIGRATION.md)。

### 🔧 环境变量管理

项目集成了完善的环境变量管理和调试功能：

- **🖥️ 启动时自动打印**: 服务端启动时自动显示所有环境变量配置状态
- **🌐 客户端调试**: 开发环境下浏览器控制台显示公共环境变量
- **🛠️ 开发工具面板**: 可视化的环境变量状态查看和调试工具
- **✅ 自动检查**: 启动前自动检查必需的环境变量配置
- **🔒 安全脱敏**: 敏感信息自动脱敏显示，保护 API 密钥等

详细配置请参考 [环境变量配置指南](./ENV_SETUP_GUIDE.md)。

### 🎯 核心价值

- **🏢 企业级架构**: 模拟真实交易公司的组织结构和工作流程
- **🤖 AI 驱动**: 集成 LangGraph 和大语言模型，提供智能化分析能力
- **📊 专业分析**: 涵盖基本面、技术面、情绪面和风险管理的全方位分析
- **🔄 实时协作**: 多智能体协同工作，模拟真实的团队决策过程
- **💼 生产就绪**: 完整的错误处理、状态管理和性能优化

## 🏗️ 系统设计思路

### 🎯 设计理念

本项目的核心设计理念是**模拟真实交易公司的运作模式**，通过多智能体协作实现专业化的金融分析和决策制定。

#### 1. **企业级分工模式**

```
交易公司组织架构
├── 分析师团队
│   ├── 基本面分析师 (Fundamental Analyst)
│   ├── 技术分析师 (Technical Analyst)
│   ├── 情绪分析师 (Sentiment Analyst)
│   └── 新闻分析师 (News Analyst)
├── 研究团队
│   ├── 多头研究员 (Bull Researcher)
│   └── 空头研究员 (Bear Researcher)
├── 风险管理团队
│   └── 风险管理师 (Risk Manager)
├── 交易执行团队
│   └── 交易员 (Trader)
└── 决策层
    └── 投资组合经理 (Portfolio Manager)
```

#### 2. **智能体协作机制**

- **并行分析**: 多个分析师同时工作，提高效率
- **结构化辩论**: 多头和空头研究员进行观点碰撞
- **层级决策**: 从分析到研究到最终决策的层级审批
- **风险控制**: 全流程风险评估和管控

#### 3. **技术架构设计原则**

- **前后端分离**: 清晰的职责边界，便于维护和扩展
- **微服务思想**: 模块化设计，每个智能体独立运行
- **事件驱动**: 基于消息和事件的异步通信
- **状态管理**: 完整的任务生命周期管理

### 🔧 核心技术选型

#### 前端技术栈

- **Next.js 15.3**: 全栈 React 框架，支持 SSR 和 API Routes
- **React 18**: 现代化的用户界面库，支持并发特性
- **TypeScript 5.0**: 类型安全，提升开发效率和代码质量
- **Tailwind CSS**: 实用优先的 CSS 框架，快速构建响应式界面
- **Framer Motion**: 流畅的动画效果，提升用户体验

#### AI 和工作流

- **LangGraph 0.3**: 智能工作流引擎，支持复杂的多智能体协作
- **LangChain.js**: 大语言模型集成框架
- **OpenAI API**: 支持 GPT-4、o1-preview 等先进模型
- **Zod**: 数据验证和类型安全

#### 数据管理

- **MySQL 8.0**: 关系型数据库，存储任务、消息和分析结果
- **TanStack Query**: 服务器状态管理，缓存和同步
- **Zustand**: 轻量级客户端状态管理
- **Socket.io**: 实时通信，支持 WebSocket

#### 开发工具

- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Husky**: Git hooks 管理
- **Jest**: 单元测试框架

## 🌟 核心特性

### 🎨 用户界面

- **现代化设计**: 基于 Tailwind CSS 的响应式设计
- **暗色主题**: 完整的明暗主题切换支持
- **移动端适配**: 完美的移动端和平板体验
- **无障碍访问**: 符合 WCAG 2.1 标准
- **国际化**: 完整的中文本地化支持

### 🚀 性能优化

- **服务端渲染**: Next.js SSR 提升首屏加载速度
- **代码分割**: 按需加载，减少包体积
- **图片优化**: Next.js Image 组件自动优化
- **缓存策略**: 多层缓存机制，提升响应速度
- **懒加载**: 组件和数据的懒加载

### 🔄 实时功能

- **WebSocket 连接**: 实时数据推送和状态同步
- **自动重连**: 网络中断时自动重连机制
- **离线支持**: 基本的离线功能支持
- **推送通知**: 重要事件的实时通知

### 📊 数据可视化

- **交互式图表**: 基于 Recharts 的丰富图表组件
- **实时更新**: 数据变化时图表自动更新
- **多维度展示**: 支持多种数据维度和视角
- **导出功能**: 支持图表和数据的导出

### 🧠 AI 智能化

- **对话式分析**: 自然语言交互进行股票分析
- **智能工作流**: LangGraph 驱动的复杂分析流程
- **上下文记忆**: 保持对话历史和分析上下文
- **自动工具调用**: 智能选择和调用分析工具

## 🏗️ 技术栈

### 核心框架

- **Next.js 14**: React 全栈框架
- **React 18**: 用户界面库
- **TypeScript**: 类型安全的 JavaScript

### 样式和 UI

- **Tailwind CSS**: 实用优先的 CSS 框架
- **Headless UI**: 无样式的可访问组件
- **Heroicons**: 精美的 SVG 图标
- **Lucide React**: 现代图标库
- **Framer Motion**: 动画库

### 数据管理

- **TanStack Query**: 服务器状态管理
- **Zustand**: 客户端状态管理
- **Axios**: HTTP 客户端

### AI 和工作流

- **LangGraph.js**: 智能工作流引擎
- **LangChain.js**: 大语言模型集成
- **Zod**: 数据验证和类型安全

### 数据可视化

- **Recharts**: React 图表库

### 开发工具

- **ESLint**: 代码检查
- **PostCSS**: CSS 处理
- **Autoprefixer**: CSS 前缀自动添加

## 🔄 当前业务流程

### 📋 任务管理流程

#### 1. **任务创建阶段**

```
用户访问首页 → 点击快速开始 → 填写分析配置 → 选择股票代码
→ 设置分析参数 → 提交创建任务 → 生成任务ID → 存储到数据库 → 跳转到分析页面
```

**关键实现**:

- 使用 `CreateAnalysisTask` 存储过程创建任务
- 支持自定义分析周期和研究深度
- AI 自动生成任务标题和描述
- 完整的表单验证和错误处理

#### 2. **任务执行阶段**

```
任务列表页面 → 选择待执行任务 → 点击开始按钮 → 更新任务状态为running
→ 调用LangGraph分析接口 → 启动多智能体工作流 → 并行执行分析任务
→ 实时更新进度状态 → 生成分析报告 → 制定交易决策 → 任务完成
```

**核心特点**:

- 任务状态实时跟踪 (`pending` → `running` → `completed`)
- 支持任务暂停、恢复和取消
- 完整的错误恢复机制
- 分布式任务调度支持

### 🤖 智能体协作流程

#### 1. **分析师团队并行工作**

```typescript
// 分析师团队配置
const analysisTeam = {
  fundamentalAnalyst: {
    role: '基本面分析师',
    tools: ['财务数据API', '公司信息API', '行业分析工具'],
    output: '基本面分析报告',
  },
  technicalAnalyst: {
    role: '技术分析师',
    tools: ['价格数据API', '技术指标计算', '图表分析'],
    output: '技术分析报告',
  },
  sentimentAnalyst: {
    role: '情绪分析师',
    tools: ['社交媒体API', '新闻情绪分析', '市场情绪指标'],
    output: '情绪分析报告',
  },
};
```

#### 2. **研究团队结构化辩论**

```typescript
// 研究团队辩论流程
const researchDebate = {
  bullResearcher: {
    role: '多头研究员',
    input: '所有分析师报告',
    task: '寻找投资机会和积极因素',
    output: '看多观点和理由',
  },
  bearResearcher: {
    role: '空头研究员',
    input: '所有分析师报告',
    task: '识别风险和消极因素',
    output: '看空观点和理由',
  },
  debateRounds: 3, // 最大辩论轮数
  consensus: '综合双方观点形成平衡视角',
};
```

### 💾 数据库架构

#### 核心表结构

```sql
-- 任务表：记录分析任务的基本信息
CREATE TABLE tasks (
    task_id VARCHAR(36) UNIQUE NOT NULL,
    ticker VARCHAR(20) NOT NULL,
    title VARCHAR(255) NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed'),
    config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 消息表：记录任务执行过程中的所有消息
CREATE TABLE messages (
    message_id VARCHAR(36) UNIQUE NOT NULL,
    task_id VARCHAR(36) NOT NULL,
    message_type ENUM('human', 'ai', 'system', 'tool'),
    content TEXT NOT NULL,
    sequence_number INT NOT NULL,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id)
);
```

#### 数据流转机制

1. **任务创建**: 用户配置 → 任务表 → 生成唯一 task_id
2. **消息记录**: 智能体交互 → 消息表 → 按序号排序
3. **工具调用**: API 调用 → 工具调用表 → 性能监控
4. **状态同步**: 数据库变更 → WebSocket 推送 → 前端更新

## 🚀 快速开始

### 环境要求

- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器

### 安装步骤

1. **克隆项目**

```bash
git clone <repository-url>
cd trading-agents-frontend
```

2. **安装依赖**

```bash
npm install
# 或
yarn install
```

3. **环境配置**

项目提供了完善的环境变量管理功能。根据你的环境选择相应的配置文件：

```bash
# 开发环境 (推荐)
cp .env.development .env.local

# 或者手动创建 .env.local
```

编辑 `.env.local` 文件，配置必要的环境变量：

```env
# 基础配置
NODE_ENV=development
NEXT_TELEMETRY_DISABLED=1

# API配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000
NEXT_PUBLIC_API_BACKEND_BASE_URL=http://localhost:5000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# AI配置 (必需)
NEXT_PUBLIC_OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_BASE_URL=https://api.nuwaapi.com/v1

# 第三方API (可选)
NEXT_PUBLIC_FINNHUB_API_KEY=your-finnhub-api-key

# 数据库配置 (可选)
DB_HOST=localhost
DB_PORT=13306
DB_NAME=trading_analysis
DB_USER=root
DB_PASSWORD=trading123
```

> 💡 **提示**: 启动时会自动检查环境变量配置，缺少必需配置时会显示详细的错误信息。

4. **检查环境配置**

```bash
# 检查环境变量配置
npm run env:check
```

5. **启动开发服务器**

```bash
# 启动开发服务器 (会自动检查环境变量)
npm run dev

# 跳过环境检查直接启动 (不推荐)
npm run dev:no-check
```

启动后你会看到：

- 🖥️ **控制台输出**: 详细的环境变量配置状态
- 🌐 **浏览器控制台**: 客户端环境变量信息 (开发环境)
- 🛠️ **开发工具**: 右下角的开发工具按钮，可查看实时配置状态

6. **访问应用**

```bash
yarn dev
```

5. **访问应用**

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 📁 项目结构

```
trading-agents-frontend/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── analysis/[id]/     # 动态分析页面
│   │   ├── api/               # API 路由
│   │   │   ├── database/      # 数据库操作接口
│   │   │   └── langgraph/     # LangGraph 分析接口
│   │   ├── create-task/       # 任务创建页面
│   │   ├── messages/          # 消息查询页面
│   │   ├── tasks/             # 任务管理页面
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局（包含Header和Footer）
│   │   └── page.tsx           # 首页（双屏设计）
│   ├── components/            # React 组件
│   │   ├── dashboard/         # 仪表板组件
│   │   ├── database/          # 数据库查询组件
│   │   ├── examples/          # 示例组件
│   │   ├── langgraph/         # LangGraph 智能体组件
│   │   ├── layout/            # 布局组件（Header/Footer）
│   │   ├── ui/                # 基础 UI 组件
│   │   ├── common/            # 通用组件
│   │   └── welcome/           # 欢迎页组件
│   ├── hooks/                 # 自定义 Hooks
│   ├── lib/                   # 工具库
│   │   ├── api.ts            # API 接口封装
│   │   ├── db.ts             # 数据库连接
│   │   ├── langgraph.ts      # LangGraph 客户端
│   │   ├── langgraph-server.ts # LangGraph 服务端
│   │   └── task-flow-database.ts # 任务流数据库操作
│   ├── store/                 # 状态管理
│   │   └── analysisStore.ts   # 分析状态管理
│   ├── types/                 # TypeScript 类型定义
│   │   ├── index.ts          # 核心类型定义
│   │   └── database.ts       # 数据库类型定义
│   └── utils/                 # 工具函数
├── database/                  # 数据库相关
│   ├── schema.sql            # 数据库结构
│   ├── init_database.sql     # 初始化脚本
│   └── migration.sql         # 迁移脚本
├── docs/                      # 项目文档
│   ├── NEW_TASK_FLOW.md      # 新任务流程文档
│   ├── SERVER_API_USAGE.md   # 服务端API使用指南
│   └── TASK_MANAGEMENT.md    # 任务管理文档
├── tests/                     # 测试文件
├── public/                    # 静态资源
├── .env.local.example        # 环境变量示例
├── next.config.js            # Next.js 配置
├── tailwind.config.js        # Tailwind CSS 配置
├── tsconfig.json             # TypeScript 配置
└── package.json              # 项目依赖
```

## 🎯 主要功能

### 1. 欢迎页面

- 项目介绍和功能展示
- 分析配置表单
- 工作流程说明

### 2. 交易仪表板

- **总览**: 分析进度和配置信息
- **代理状态**: 实时监控各代理工作状态
- **实时数据**: 股价、技术指标、新闻、基本面数据
- **分析报告**: 各代理生成的详细报告
- **交易决策**: 最终的交易建议和参数

### 3. 实时功能

- WebSocket 连接实时更新
- 自动数据刷新
- 状态同步

### 4. **LangGraph 智能分析**

- **对话式分析**: 通过自然语言进行股票分析
- **智能工作流**: 可视化的分析流程和状态管理
- **工具集成**: 自动调用合适的分析工具
- **内存管理**: 保持对话上下文和分析历史

### 5. **数据管理系统**

- **消息记录**: 完整的分析过程消息记录和查询
- **数据库集成**: 基于 MySQL 的可靠数据存储
- **API 接口**: RESTful API 设计，支持完整的 CRUD 操作
- **数据同步**: 实时数据同步和状态更新

## 🔌 API 接口设计

### 📋 任务管理接口

#### 任务 CRUD 操作

```typescript
// 创建任务
POST /api/database/tasks
{
  "ticker": "AAPL",
  "title": "苹果公司股票分析",
  "description": "综合分析苹果公司投资价值",
  "config": { "analysisType": "comprehensive" },
  "research_depth": "standard",
  "analysis_period": "1m"
}

// 获取任务列表
GET /api/database/tasks

// 获取单个任务
GET /api/database/tasks/{taskId}

// 更新任务状态
PATCH /api/database/tasks/{taskId}/status
{
  "status": "running"
}
```

#### 任务执行接口

```typescript
// 启动分析任务
POST /api/langgraph/analysis/start
{
  "ticker": "AAPL",
  "researchDepth": "standard",
  "analysisPeriod": "1m",
  "selectedAnalysts": ["fundamental", "technical", "sentiment"]
}

// 获取分析状态
GET /api/langgraph/analysis/status?id={analysisId}

// 流式分析接口
POST /api/langgraph/stream
{
  "ticker": "AAPL",
  "config": { "analysisType": "comprehensive" }
}
```

### 💬 消息管理接口

#### 消息操作

```typescript
// 添加消息
POST /api/database/messages
{
  "task_id": "uuid-task-id",
  "message_type": "ai",
  "content": "分析结果内容",
  "metadata": { "agent": "fundamental_analyst" },
  "thread_id": "thread-123"
}

// 查询消息
GET /api/database/messages?task_id={taskId}&limit=50&offset=0

// 按类型过滤消息
GET /api/database/messages?task_id={taskId}&message_type=ai

// 按线程过滤消息
GET /api/database/messages?task_id={taskId}&thread_id={threadId}
```

### 🤖 LangGraph 智能体接口

#### 对话接口

```typescript
// 发送聊天消息
POST /api/langgraph/chat
{
  "message": "请分析 NVDA 的投资价值",
  "threadId": "optional-thread-id"
}

// 股票分析接口
POST /api/langgraph/analyze
{
  "ticker": "NVDA",
  "task_id": "uuid-task-id",
  "config": {
    "analysisType": "comprehensive",
    "includeRisk": true,
    "includeSentiment": true
  }
}

// 获取会话状态
GET /api/langgraph/state?threadId={threadId}

// 清除会话
DELETE /api/langgraph/state?threadId={threadId}
```

## 🎨 界面设计

### 设计原则

- **简洁明了**: 清晰的信息层次
- **响应式**: 适配各种屏幕尺寸
- **可访问性**: 符合 WCAG 标准
- **一致性**: 统一的设计语言

### 主题配置

- 支持深色/浅色主题
- 自定义颜色方案
- 响应式字体大小

## 🔧 开发指南

### 代码规范

- 使用 ESLint 进行代码检查
- 遵循 React/TypeScript 最佳实践
- 组件采用函数式编程

### 组件开发

```tsx
// 示例组件结构
'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

interface ComponentProps {
  // 定义 props 类型
}

export function Component({}: ComponentProps) {
  // 组件逻辑
  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
      {/* 组件内容 */}
    </motion.div>
  );
}
```

### 状态管理

```tsx
// 使用 TanStack Query 管理服务器状态
const { data, isLoading } = useQuery({
  queryKey: ['key'],
  queryFn: fetchData,
});

// 使用 Zustand 管理客户端状态
const useStore = create((set) => ({
  // 状态定义
}));
```

## 📦 构建和部署

### 构建生产版本

```bash
npm run build
npm run start
```

### 类型检查

```bash
npm run type-check
```

### 代码检查

```bash
npm run lint
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源。详见 [LICENSE](LICENSE) 文件。

## ⚠️ 免责声明

本框架仅用于研究目的。交易表现可能因多种因素而异，包括所选的骨干语言模型、模型温度、交易周期、数据质量和其他非确定性因素。**不构成财务、投资或交易建议。**

## 🤖 AI 交互工具集

本项目集成了丰富的 AI 交互工具，支持智能化的股票分析和对话式交互：

### 核心 AI 工具

#### 1. **股票分析工具** (`stockAnalysisTool`)

- **功能**: 综合股票分析
- **支持类型**:
  - `fundamentals` - 基本面分析
  - `technical` - 技术面分析
  - `news` - 新闻分析
  - `sentiment` - 情绪分析
- **参数**: 股票代码(ticker)、分析类型(analysisType)

#### 2. **市场数据工具** (`marketDataTool`)

- **功能**: 获取实时市场数据
- **数据类型**:
  - `price` - 价格数据
  - `volume` - 成交量数据
  - `indicators` - 技术指标
- **时间周期**: 1 天、1 周、1 月、3 月、1 年
- **参数**: 股票代码(ticker)、数据类型(dataType)、时间周期(period)

#### 3. **新闻分析工具** (`newsAnalysisTool`)

- **功能**: 分析股票相关新闻
- **特性**:
  - 新闻情绪分析
  - 影响评估
  - 舆情监控
- **参数**: 股票代码(ticker)、是否情绪分析(sentiment)

#### 4. **风险评估工具** (`riskAssessmentTool`)

- **功能**: 投资风险评估
- **评估维度**:
  - 单股风险评估
  - 投资组合风险分析
  - 风险等级评定
- **参数**: 股票代码(ticker)、投资组合(portfolio)、风险偏好(riskLevel)

### AI 交互组件

#### 1. **LangGraph 智能代理** (`TradingAgent`)

- **核心功能**:
  - `analyze(ticker, config)` - 股票分析
  - `chat(message, threadId)` - 对话交互
  - `streamAnalysis(ticker, config)` - 流式分析
  - `getState(threadId)` - 获取状态

#### 2. **对话式分析界面** (`LangGraphChat`)

- **特性**:
  - 实时对话交互
  - 消息历史管理
  - 流式响应显示
  - 错误处理和重试

#### 3. **工作流可视化** (`WorkflowVisualization`)

- **功能**:
  - 实时工作流状态监控
  - 节点执行进度显示
  - 执行路径可视化
  - 性能指标统计

#### 4. **智能配置界面** (`LangGraphConfig`)

- **配置项**:
  - LLM 模型选择和参数
  - 工作流执行策略
  - 内存管理设置
  - 调试和监控选项

### React Hooks

#### 1. **useLangGraphAgent**

- **状态管理**:

  - `messages` - 对话消息列表
  - `isProcessing` - 处理状态
  - `currentStep` - 当前执行步骤
  - `analysisResults` - 分析结果
  - `tradingDecision` - 交易决策
  - `error` - 错误信息

- **操作方法**:
  - `analyzeStock(ticker, config)` - 开始股票分析
  - `sendMessage(message)` - 发送聊天消息
  - `streamAnalysis(ticker, config)` - 流式分析
  - `getAgentState()` - 获取代理状态
  - `clearConversation()` - 清除对话
  - `retry()` - 重试操作

### 工作流节点

#### 1. **分析节点**

- `start` - 开始节点
- `data_collection` - 数据收集
- `fundamental_analysis` - 基本面分析
- `technical_analysis` - 技术分析
- `sentiment_analysis` - 情绪分析
- `risk_assessment` - 风险评估
- `decision_making` - 决策制定
- `end` - 结束节点

#### 2. **工具节点** (`ToolNode`)

- 自动工具调用
- 并行工具执行
- 工具结果聚合
- 错误处理和重试

### 使用示例

```typescript
// 1. 基础股票分析
const { analyzeStock } = useLangGraphAgent();
await analyzeStock('NVDA', {
  analysisType: 'comprehensive',
  includeRisk: true,
  includeSentiment: true,
});

// 2. 对话式交互
const { sendMessage } = useLangGraphAgent();
await sendMessage('请分析 AAPL 的投资价值');

// 3. 流式分析
const { streamAnalysis } = useLangGraphAgent();
for await (const chunk of streamAnalysis('TSLA')) {
  console.log('分析进度:', chunk);
}

// 4. 自定义工具
import { tool } from '@langchain/core/tools';
import { z } from 'zod';

const customTool = tool(
  async ({ param1, param2 }) => {
    // 自定义工具逻辑
    return 'result';
  },
  {
    name: 'custom_tool',
    description: '自定义工具描述',
    schema: z.object({
      param1: z.string(),
      param2: z.number(),
    }),
  }
);
```

## 🚀 未来发展规划

### 📈 短期目标 (3-6 个月)

#### 1. **功能增强**

- **实时数据集成**: 集成更多实时数据源（Bloomberg、Reuters、Alpha Vantage）
- **高级图表**: 增加更多技术分析图表和指标
- **移动端优化**: 开发专门的移动端界面和 PWA 支持
- **多语言支持**: 扩展到英文、日文等多语言界面

#### 2. **性能优化**

- **缓存策略**: 实现多层缓存机制，提升响应速度
- **数据库优化**: 优化查询性能，支持更大数据量
- **CDN 集成**: 静态资源 CDN 加速
- **服务端渲染**: 优化 SEO 和首屏加载速度

#### 3. **用户体验**

- **个性化设置**: 用户偏好设置和自定义界面
- **通知系统**: 实时通知和邮件提醒功能
- **快捷操作**: 键盘快捷键和批量操作
- **无障碍访问**: 完善的无障碍功能支持

### 🎯 中期目标 (6-12 个月)

#### 1. **智能体扩展**

- **新增专业角色**:
  - 宏观经济分析师 (Macro Economist)
  - 行业分析师 (Sector Analyst)
  - 量化分析师 (Quantitative Analyst)
  - ESG 分析师 (ESG Analyst)

#### 2. **高级分析功能**

- **投资组合分析**: 多股票组合分析和优化
- **回测系统**: 历史数据回测和策略验证
- **风险建模**: 高级风险模型和压力测试
- **情景分析**: 多种市场情景下的表现分析

#### 3. **协作功能**

- **团队协作**: 多用户协作分析和讨论
- **分享机制**: 分析报告分享和导出
- **版本控制**: 分析版本管理和历史追踪
- **审批流程**: 企业级审批和权限管理

### 🌟 长期愿景 (1-2 年)

#### 1. **AI 能力升级**

- **多模态分析**: 支持图像、音频等多模态数据分析
- **自主学习**: 智能体自主学习和能力提升
- **预测建模**: 高精度的价格和趋势预测模型
- **自然语言生成**: 更自然的分析报告生成

#### 2. **平台化发展**

- **插件系统**: 支持第三方插件和扩展
- **API 开放**: 开放 API 供第三方集成
- **白标解决方案**: 为金融机构提供白标服务
- **云原生架构**: 完全云原生的微服务架构

#### 3. **生态系统建设**

- **开发者社区**: 建设活跃的开发者社区
- **培训认证**: 专业的培训课程和认证体系
- **合作伙伴**: 与金融机构和数据提供商合作
- **学术研究**: 与高校合作进行学术研究

### 🔬 技术创新方向

#### 1. **前沿 AI 技术**

- **大模型优化**: 针对金融领域的专用大模型
- **联邦学习**: 保护隐私的分布式学习
- **强化学习**: 基于强化学习的交易策略
- **知识图谱**: 金融知识图谱构建和应用

#### 2. **架构演进**

- **边缘计算**: 支持边缘计算和离线分析
- **区块链集成**: 区块链技术在数据验证中的应用
- **量子计算**: 为未来量子计算做技术储备
- **绿色计算**: 环保和可持续的计算架构

#### 3. **数据科学**

- **实时流处理**: 大规模实时数据流处理
- **图数据库**: 复杂关系数据的图数据库应用
- **时序数据库**: 专门的时序数据存储和分析
- **数据湖架构**: 统一的数据湖和数据治理

### 💼 商业化路径

#### 1. **产品矩阵**

- **社区版**: 免费的基础功能版本
- **专业版**: 面向个人投资者的付费版本
- **企业版**: 面向机构的企业级解决方案
- **云服务**: SaaS 模式的云端服务

#### 2. **市场策略**

- **垂直领域**: 专注特定行业或市场的解决方案
- **地域扩展**: 扩展到全球主要金融市场
- **合规认证**: 获得各地金融监管认证
- **品牌建设**: 建立行业领先的品牌影响力

## 🔗 相关链接

- [TradingAgents 后端项目](https://github.com/TauricResearch/TradingAgents)
- [研究论文](https://arxiv.org/abs/2412.20138)
- [LangGraph 使用指南](./LANGGRAPH_GUIDE.md)
- [LangGraph.js 官方文档](https://langchain-ai.github.io/langgraphjs/)
- [Tauric Research](https://tauric.ai/)
- [Discord 社区](https://discord.com/invite/hk9PGKShPK)

## 📞 支持与贡献

### 🤝 如何贡献

我们欢迎社区贡献！您可以通过以下方式参与：

1. **代码贡献**: 提交 Pull Request 改进代码
2. **问题反馈**: 通过 GitHub Issues 报告 bug 或建议功能
3. **文档完善**: 帮助改进项目文档和教程
4. **测试反馈**: 参与测试并提供反馈意见
5. **社区建设**: 在 Discord 社区中帮助其他用户

### 📧 联系方式

如有问题或建议，请通过以下方式联系：

- **GitHub Issues**: 技术问题和功能建议
- **Discord 社区**: 实时讨论和社区支持
- **邮箱**: <EMAIL>
- **官网**: https://tauric.ai

### ⚖️ 开源协议

本项目基于 MIT 许可证开源，详见 [LICENSE](LICENSE) 文件。

### ⚠️ 免责声明

本框架仅用于研究和教育目的。交易表现可能因多种因素而异，包括所选的骨干语言模型、模型温度、交易周期、数据质量和其他非确定性因素。**不构成财务、投资或交易建议。请在使用前咨询专业的财务顾问。**

---

<p align="center">
  <strong>🚀 让AI驱动的金融分析更智能、更专业、更可靠</strong>
</p>

<p align="center">
  Made with ❤️ by <a href="https://tauric.ai">Tauric Research</a>
</p>
