# 邮箱验证码功能实现文档

## 概述

本文档详细说明了为 TradingAgent 注册模块实现的邮箱验证码功能。该功能提供了安全的用户注册流程，确保用户提供的邮箱地址真实有效。

## 功能特性

### 🔐 安全特性
- **6位数字验证码**：随机生成，安全性高
- **时效性控制**：验证码有效期10分钟
- **频率限制**：1分钟内只能发送一次验证码
- **尝试次数限制**：最多5次验证尝试
- **防滥用机制**：24小时内最多发送10次验证码

### 📧 邮件功能
- **美观的HTML邮件模板**：专业的邮件设计
- **多种验证码类型**：支持注册、密码重置、邮箱更换
- **发送状态跟踪**：完整的邮件发送日志
- **错误处理**：详细的错误信息和重试机制

### 🎨 用户体验
- **实时验证**：输入完成自动验证
- **智能输入**：支持粘贴6位验证码
- **倒计时显示**：清晰的重发倒计时
- **状态反馈**：实时的验证状态提示

## 技术架构

### 数据库设计

#### 1. 验证码表 (`email_verification_codes`)
```sql
CREATE TABLE email_verification_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code_id VARCHAR(36) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    code VARCHAR(6) NOT NULL,
    code_type ENUM('register', 'reset_password', 'change_email'),
    is_used BOOLEAN DEFAULT FALSE,
    attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 5,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- 索引优化
    INDEX idx_email_code_type (email, code_type)
);
```

#### 2. 邮件发送日志表 (`email_send_logs`)
```sql
CREATE TABLE email_send_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    log_id VARCHAR(36) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    email_type ENUM('verification_code', 'welcome', 'password_reset'),
    status ENUM('pending', 'sent', 'failed'),
    error_message TEXT,
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### API 接口

#### 1. 发送验证码 API
```typescript
POST /api/auth/send-verification-code
Content-Type: application/json

{
  "email": "<EMAIL>",
  "type": "register" // register | reset_password | change_email
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "验证码已发送到您的邮箱",
  "data": {
    "codeId": "uuid-string",
    "email": "<EMAIL>",
    "expiresInMinutes": 10
  }
}
```

#### 2. 验证验证码 API
```typescript
POST /api/auth/verify-code
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456",
  "type": "register"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "验证码验证成功",
  "data": {
    "email": "<EMAIL>",
    "type": "register",
    "verified": true
  }
}
```

#### 3. 注册 API (已更新)
```typescript
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "username": "testuser",
  "password": "SecurePass123!",
  "verificationCode": "123456"
}
```

### 前端组件

#### 1. VerificationCodeInput 组件
```typescript
interface VerificationCodeInputProps {
  email: string;
  type?: 'register' | 'reset_password' | 'change_email';
  onVerified: (code: string) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
}
```

**特性：**
- 6个独立的数字输入框
- 自动跳转到下一个输入框
- 支持粘贴完整验证码
- 实时验证和错误提示
- 重发验证码功能

#### 2. RegisterForm 组件
```typescript
interface RegisterFormProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}
```

**特性：**
- 两步注册流程
- 密码强度检测
- 实时表单验证
- 集成验证码验证

## 环境配置

### 1. 邮件服务配置
在 `.env` 文件中配置 SMTP 服务：

```env
# Gmail 配置示例
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
```

### 2. 获取 Gmail 应用密码
1. 登录 Google 账户
2. 启用两步验证
3. 生成应用专用密码
4. 使用应用密码作为 `SMTP_PASS`

### 3. 其他邮件服务商
支持所有标准 SMTP 服务：
- **Outlook/Hotmail**: smtp-mail.outlook.com:587
- **Yahoo**: smtp.mail.yahoo.com:587
- **QQ邮箱**: smtp.qq.com:587
- **163邮箱**: smtp.163.com:25

## 安装和部署

### 1. 安装依赖
```bash
npm install nodemailer
npm install --save-dev @types/nodemailer
```

### 2. 数据库迁移
```bash
# 执行数据库迁移脚本
mysql -u username -p database_name < database/migrations/add_email_verification_codes.sql
```

### 3. 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

## 使用示例

### 1. 基本注册流程
```typescript
import { RegisterForm } from '@/components/auth/RegisterForm';

function RegisterPage() {
  return (
    <RegisterForm
      onSuccess={() => {
        console.log('注册成功！');
        router.push('/dashboard');
      }}
      onError={(error) => {
        console.error('注册失败:', error);
      }}
    />
  );
}
```

### 2. 独立使用验证码组件
```typescript
import { VerificationCodeInput } from '@/components/auth/VerificationCodeInput';

function MyComponent() {
  return (
    <VerificationCodeInput
      email="<EMAIL>"
      type="register"
      onVerified={(code) => {
        console.log('验证码验证成功:', code);
      }}
      onError={(error) => {
        console.error('验证失败:', error);
      }}
    />
  );
}
```

## 错误处理

### 常见错误码
- **400**: 请求参数错误
- **409**: 邮箱已被注册
- **429**: 发送频率过快
- **500**: 服务器内部错误

### 验证码验证结果
- **success**: 验证成功
- **invalid**: 验证码错误
- **expired**: 验证码已过期
- **used**: 验证码已使用
- **max_attempts**: 尝试次数超限

## 安全考虑

### 1. 防暴力破解
- 限制验证尝试次数（最多5次）
- 验证码过期时间（10分钟）
- IP地址记录和监控

### 2. 防垃圾邮件
- 发送频率限制（1分钟1次）
- 24小时发送次数限制（10次）
- 邮箱格式验证

### 3. 数据保护
- 验证码不在响应中返回
- 敏感信息加密存储
- 定期清理过期数据

## 监控和日志

### 1. 邮件发送监控
```sql
-- 查看邮件发送统计
SELECT 
  DATE(created_at) as date,
  email_type,
  status,
  COUNT(*) as count
FROM email_send_logs 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_at), email_type, status;
```

### 2. 验证码使用统计
```sql
-- 查看验证码使用情况
SELECT 
  DATE(created_at) as date,
  code_type,
  COUNT(*) as total_sent,
  SUM(CASE WHEN is_used = TRUE THEN 1 ELSE 0 END) as used_count,
  AVG(attempts) as avg_attempts
FROM email_verification_codes 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_at), code_type;
```

## 性能优化

### 1. 数据库优化
- 添加适当的索引
- 定期清理过期数据
- 使用连接池

### 2. 邮件发送优化
- 异步发送邮件
- 使用邮件队列
- 错误重试机制

### 3. 前端优化
- 组件懒加载
- 防抖输入处理
- 缓存验证状态

## 扩展功能

### 1. 短信验证码
可以扩展支持短信验证码：
```typescript
interface VerificationOptions {
  method: 'email' | 'sms';
  contact: string; // 邮箱或手机号
}
```

### 2. 多语言支持
邮件模板支持多语言：
```typescript
interface EmailTemplate {
  subject: Record<string, string>;
  content: Record<string, string>;
}
```

### 3. 自定义邮件模板
支持自定义邮件模板：
```typescript
interface CustomTemplate {
  templateId: string;
  variables: Record<string, any>;
}
```

## 故障排除

### 1. 邮件发送失败
- 检查 SMTP 配置
- 验证邮箱密码
- 检查网络连接
- 查看错误日志

### 2. 验证码收不到
- 检查垃圾邮件文件夹
- 验证邮箱地址格式
- 检查发送频率限制
- 查看邮件发送日志

### 3. 验证失败
- 检查验证码是否过期
- 验证尝试次数是否超限
- 检查邮箱地址匹配
- 查看验证码状态

## 总结

邮箱验证码功能为 TradingAgent 提供了安全可靠的用户注册验证机制。通过完善的前后端实现、详细的错误处理和全面的安全措施，确保了用户注册流程的安全性和用户体验的流畅性。

该功能具有良好的扩展性，可以轻松适配其他验证场景，如密码重置、邮箱更换等，为整个应用的用户认证体系提供了坚实的基础。