'use client';

import React from 'react';
import { ExclamationTriangleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // 这里可以添加错误报告逻辑
    // reportError(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} retry={this.handleRetry} />;
      }

      return <DefaultErrorFallback error={this.state.error!} retry={this.handleRetry} />;
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error: Error;
  retry: () => void;
}

function DefaultErrorFallback({ error, retry }: ErrorFallbackProps) {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="max-w-md w-full">
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
            </div>
            <CardTitle className="text-red-900 dark:text-red-100">
              出现错误
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-slate-600 dark:text-slate-400">
            应用程序遇到了一个意外错误。请尝试刷新页面或联系技术支持。
          </p>
          
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium text-slate-700 dark:text-slate-300">
                错误详情 (开发模式)
              </summary>
              <div className="mt-2 p-3 bg-slate-100 dark:bg-slate-800 rounded text-sm font-mono text-slate-800 dark:text-slate-200 overflow-auto">
                <div className="font-semibold text-red-600 dark:text-red-400 mb-2">
                  {error.name}: {error.message}
                </div>
                <pre className="whitespace-pre-wrap text-xs">
                  {error.stack}
                </pre>
              </div>
            </details>
          )}
          
          <div className="flex space-x-3">
            <Button onClick={retry} className="flex items-center space-x-2">
              <ArrowPathIcon className="h-4 w-4" />
              <span>重试</span>
            </Button>
            <Button 
              variant="secondary" 
              onClick={() => window.location.reload()}
            >
              刷新页面
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// 用于函数组件的错误边界 Hook
export function useErrorHandler() {
  return (error: Error, errorInfo?: React.ErrorInfo) => {
    console.error('Error caught by error handler:', error, errorInfo);
    
    // 这里可以添加错误报告逻辑
    // reportError(error, errorInfo);
  };
}

// 异步错误处理组件
interface AsyncErrorBoundaryProps {
  children: React.ReactNode;
  onError?: (error: Error) => void;
}

export function AsyncErrorBoundary({ children, onError }: AsyncErrorBoundaryProps) {
  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      if (onError) {
        onError(new Error(event.reason));
      }
    };

    const handleError = (event: ErrorEvent) => {
      console.error('Global error:', event.error);
      if (onError) {
        onError(event.error);
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, [onError]);

  return <>{children}</>;
}
