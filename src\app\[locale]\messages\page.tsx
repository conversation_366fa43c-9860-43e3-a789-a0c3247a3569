'use client';

import MessagesPage from './MessagesPage';

import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Message, messageApi, Pagination } from '@/lib/api';
import useUserStore from '@/store/userStore';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'react-hot-toast';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// 消息类型配置
const MESSAGE_TYPES = {
  human: { label: '用户', color: 'bg-blue-100 text-blue-800 border-blue-200', icon: '👤' },
  ai: { label: 'AI', color: 'bg-green-100 text-green-800 border-green-200', icon: '🤖' },
  system: { label: '系统', color: 'bg-gray-100 text-gray-800 border-gray-200', icon: '⚙️' },
  tool: { label: '工具', color: 'bg-purple-100 text-purple-800 border-purple-200', icon: '🔧' },
};

// 使用 useSearchParams 的组件需要用 Suspense 包装
function MessagesContent() {
  const router = useRouter();
  const { user, initialized, loading: authLoading } = useUserStore();
  const searchParams = useSearchParams();
  const taskId = searchParams?.get('task_id');
  const conversationId = searchParams?.get('conversation_id');

  // 状态管理
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<Pagination>({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false,
  });

  // 过滤和显示选项
  const [selectedMessageType, setSelectedMessageType] = useState<string>('');
  const [renderMarkdown, setRenderMarkdown] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // 获取消息数据
  const fetchMessages = useCallback(
    async (offset = 0, append = false) => {
      if (!taskId && !conversationId) {
        setError('缺少必要的参数：task_id 或 conversation_id');
        setLoading(false);
        return;
      }

      try {
        if (!append) setLoading(true);
        else setLoadingMore(true);

        const apiParams: {
          limit: number;
          offset: number;
          taskId?: string;
          conversationId?: string;
          messageType?: string;
        } = {
          limit: pagination.limit,
          offset: offset,
        };

        if (taskId) apiParams.taskId = taskId;
        if (conversationId) apiParams.conversationId = conversationId;
        if (selectedMessageType) apiParams.messageType = selectedMessageType;

        const response = await messageApi.getMessages(apiParams);

        if (!response.success || !response.data) {
          throw new Error(response.message || '获取消息失败');
        }

        const { messages: newMessages, pagination: newPagination } = response.data;

        if (append) {
          setMessages((prev) => [...prev, ...newMessages]);
        } else {
          setMessages(newMessages);
        }

        setPagination(newPagination);
        setError(null);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '获取消息失败';
        setError(errorMessage);
        setMessages([]);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [taskId, conversationId, selectedMessageType, pagination.limit]
  );

  // 加载更多消息
  const loadMore = useCallback(() => {
    if (pagination.hasMore && !loadingMore) {
      fetchMessages(pagination.offset + pagination.limit, true);
    }
  }, [fetchMessages, pagination.hasMore, pagination.offset, pagination.limit, loadingMore]);

  // 过滤消息（基于搜索查询）
  const filteredMessages = useMemo(() => {
    if (!searchQuery.trim()) return messages;

    const query = searchQuery.toLowerCase();
    return messages.filter(
      (message) =>
        message.content.toLowerCase().includes(query) ||
        message.message_id.toLowerCase().includes(query) ||
        MESSAGE_TYPES[message.message_type].label.toLowerCase().includes(query)
    );
  }, [messages, searchQuery]);

  // 初始加载
  useEffect(() => {
    if (initialized && user) {
      fetchMessages();
    }
  }, [fetchMessages, initialized, user]);

  useEffect(() => {
    if (initialized && !user) {
      router.push('/login');
    }
  }, [initialized, user, router]);

  // 消息类型过滤变化时重新加载
  useEffect(() => {
    if (messages.length > 0) {
      fetchMessages();
    }
  }, [selectedMessageType, fetchMessages, messages.length]);

  // Markdown渲染组件
  const MessageContent = ({
    content,
    renderAsMarkdown,
  }: {
    content: string;
    renderAsMarkdown: boolean;
  }) => {
    if (renderAsMarkdown) {
      return (
        <div className="prose prose-sm max-w-none prose-gray prose-headings:text-gray-900 prose-p:text-gray-700 prose-strong:text-gray-900">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              code: ({ node, className, children, inline, ...props }: any) => {
                const match = /language-(\w+)/.exec(className || '');
                return !inline && match ? (
                  <pre className="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto border shadow-sm my-4">
                    <code className={className} {...props}>
                      {children}
                    </code>
                  </pre>
                ) : (
                  <code
                    className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono border"
                    {...props}
                  >
                    {children}
                  </code>
                );
              },
              table: ({ children }) => (
                <div className="overflow-x-auto my-6 rounded-lg border border-gray-200 shadow-sm">
                  <table className="min-w-full divide-y divide-gray-200">{children}</table>
                </div>
              ),
              thead: ({ children }) => (
                <thead className="bg-slate-50 dark:bg-slate-800">{children}</thead>
              ),
              th: ({ children }) => (
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {children}
                </th>
              ),
              td: ({ children }) => (
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{children}</td>
              ),
              a: ({ href, children }) => (
                <a
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                >
                  {children}
                </a>
              ),
              blockquote: ({ children }) => (
                <blockquote className="border-l-4 border-blue-500 pl-6 py-3 my-6 bg-blue-50 rounded-r-lg shadow-sm">
                  <div className="text-gray-700 italic">{children}</div>
                </blockquote>
              ),
              ul: ({ children }) => (
                <ul className="list-disc list-inside space-y-2 my-4 text-gray-700">{children}</ul>
              ),
              ol: ({ children }) => (
                <ol className="list-decimal list-inside space-y-2 my-4 text-gray-700">
                  {children}
                </ol>
              ),
              li: ({ children }) => <li className="leading-relaxed">{children}</li>,
              h1: ({ children }) => (
                <h1 className="text-2xl font-bold text-gray-900 mt-8 mb-4 pb-2 border-b border-gray-200">
                  {children}
                </h1>
              ),
              h2: ({ children }) => (
                <h2 className="text-xl font-semibold text-gray-900 mt-6 mb-3">{children}</h2>
              ),
              h3: ({ children }) => (
                <h3 className="text-lg font-medium text-gray-900 mt-4 mb-2">{children}</h3>
              ),
              p: ({ children }) => <p className="text-gray-700 leading-relaxed my-3">{children}</p>,
            }}
          >
            {content}
          </ReactMarkdown>
        </div>
      );
    } else {
      return (
        <pre className="whitespace-pre-wrap break-words font-mono text-sm bg-slate-50 dark:bg-slate-800 text-slate-800 dark:text-slate-200 p-4 rounded-lg border border-slate-200 dark:border-slate-700 shadow-sm overflow-x-auto">
          {content}
        </pre>
      );
    }
  };

  if (!initialized || authLoading) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (loading && messages.length === 0) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600 dark:text-slate-400">加载消息中...</p>
        </div>
      </div>
    );
  }

  if (error && messages.length === 0) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">❌ 加载失败</div>
          <p className="text-slate-600 dark:text-slate-400 mb-4">{error}</p>
          <button
            onClick={() => fetchMessages()}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 页面标题和导航 */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">消息记录</h1>
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                {taskId && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    任务: {taskId.substring(0, 8)}...
                  </span>
                )}
                {conversationId && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    对话: {conversationId.substring(0, 8)}...
                  </span>
                )}
              </div>
            </div>
            <Link
              href="/tasks"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
            >
              <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              返回任务列表
            </Link>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">{pagination.total}</div>
                <div className="text-sm text-gray-600">总消息数</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-blue-600">
                  {messages.filter((m) => m.message_type === 'human').length}
                </div>
                <div className="text-sm text-gray-600">用户消息</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-green-600">
                  {messages.filter((m) => m.message_type === 'ai').length}
                </div>
                <div className="text-sm text-gray-600">AI回复</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-purple-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-purple-600">
                  {
                    messages.filter((m) => m.message_type === 'system' || m.message_type === 'tool')
                      .length
                  }
                </div>
                <div className="text-sm text-gray-600">系统/工具</div>
              </div>
            </div>
          </div>
        </div>

        {/* 过滤器和搜索 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 搜索框 */}
            <div className="lg:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-3">搜索消息</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索消息内容、ID..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white text-gray-900 placeholder-gray-500"
                />
              </div>
            </div>

            {/* 消息类型过滤 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">消息类型</label>
              <select
                value={selectedMessageType}
                onChange={(e) => setSelectedMessageType(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white text-gray-900 appearance-none cursor-pointer hover:border-gray-400"
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                  backgroundPosition: 'right 0.75rem center',
                  backgroundRepeat: 'no-repeat',
                  backgroundSize: '1.5em 1.5em',
                }}
              >
                <option value="" className="text-gray-900">
                  全部类型
                </option>
                <option value="human" className="text-gray-900">
                  👤 用户消息
                </option>
                <option value="ai" className="text-gray-900">
                  🤖 AI回复
                </option>
                <option value="system" className="text-gray-900">
                  ⚙️ 系统消息
                </option>
                <option value="tool" className="text-gray-900">
                  🔧 工具消息
                </option>
              </select>
            </div>

            {/* 显示模式 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">显示模式</label>
              <div className="flex rounded-lg border border-gray-300 overflow-hidden">
                <button
                  onClick={() => setRenderMarkdown(false)}
                  className={`flex-1 px-4 py-3 text-sm font-medium transition-all ${
                    !renderMarkdown
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <span className="flex items-center justify-center">
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
                      />
                    </svg>
                    原始文本
                  </span>
                </button>
                <button
                  onClick={() => setRenderMarkdown(true)}
                  className={`flex-1 px-4 py-3 text-sm font-medium transition-all ${
                    renderMarkdown
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <span className="flex items-center justify-center">
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                      />
                    </svg>
                    Markdown
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 消息列表 */}
        <div className="space-y-4">
          {filteredMessages.length === 0 ? (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                  {searchQuery ? (
                    <svg
                      className="w-8 h-8 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="w-8 h-8 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                      />
                    </svg>
                  )}
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchQuery ? '没有找到匹配的消息' : '暂无消息记录'}
                </h3>
                <p className="text-gray-500 mb-6">
                  {searchQuery
                    ? '尝试调整搜索条件或清除过滤器来查看更多消息。'
                    : '该任务还没有生成任何消息记录。'}
                </p>
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                    清除搜索条件
                  </button>
                )}
              </div>
            </div>
          ) : (
            <>
              {filteredMessages.map((message, index) => (
                <div
                  key={message.id}
                  className={`bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 ${
                    message.message_type === 'human'
                      ? 'border-l-4 border-l-blue-500'
                      : message.message_type === 'ai'
                      ? 'border-l-4 border-l-green-500'
                      : message.message_type === 'system'
                      ? 'border-l-4 border-l-gray-500'
                      : 'border-l-4 border-l-purple-500'
                  }`}
                >
                  <div className="p-6">
                    {/* 消息头部 */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4">
                      <div className="flex items-center space-x-3">
                        <span
                          className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${
                            MESSAGE_TYPES[message.message_type].color
                          }`}
                        >
                          <span className="mr-1.5">{MESSAGE_TYPES[message.message_type].icon}</span>
                          {MESSAGE_TYPES[message.message_type].label}
                        </span>
                        <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600">
                          #{message.sequence_number}
                        </span>
                        <span className="text-xs text-gray-400 font-mono bg-gray-50 px-2 py-1 rounded">
                          {message.message_id.substring(0, 8)}...
                        </span>
                      </div>
                      <div className="flex items-center text-sm text-gray-500">
                        <svg
                          className="w-4 h-4 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        {format(new Date(message.created_at), 'yyyy-MM-dd HH:mm:ss', {
                          locale: zhCN,
                        })}
                      </div>
                    </div>

                    {/* 消息内容 */}
                    <div className="mb-4">
                      <MessageContent content={message.content} renderAsMarkdown={renderMarkdown} />
                    </div>

                    {/* 元数据和其他信息 */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-4 border-t border-gray-100">
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        {message.parent_message_id && (
                          <span className="inline-flex items-center px-2 py-1 rounded-md bg-yellow-50 text-yellow-700 border border-yellow-200">
                            <svg
                              className="w-3 h-3 mr-1"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"
                              />
                            </svg>
                            回复: {message.parent_message_id.substring(0, 8)}...
                          </span>
                        )}
                        {message.metadata && (
                          <details className="cursor-pointer group">
                            <summary className="inline-flex items-center px-2 py-1 rounded-md bg-gray-50 text-gray-600 hover:bg-gray-100 transition-colors">
                              <svg
                                className="w-3 h-3 mr-1"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                              </svg>
                              元数据
                            </summary>
                            <div className="mt-3 p-3 bg-gray-50 rounded-lg border">
                              <pre className="text-xs text-gray-700 overflow-x-auto whitespace-pre-wrap">
                                {JSON.stringify(message.metadata, null, 2)}
                              </pre>
                            </div>
                          </details>
                        )}
                      </div>
                      <div className="flex items-center text-xs text-gray-500">
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                        {message.content.length.toLocaleString()} 字符
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {/* 加载更多按钮 */}
              {pagination.hasMore && (
                <div className="text-center py-8">
                  <button
                    onClick={loadMore}
                    disabled={loadingMore}
                    className="inline-flex items-center px-8 py-4 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    {loadingMore ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                        加载中...
                      </>
                    ) : (
                      <>
                        <svg
                          className="w-5 h-5 mr-3"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 14l-7 7m0 0l-7-7m7 7V3"
                          />
                        </svg>
                        加载更多 (还有 {(pagination.total - messages.length).toLocaleString()} 条)
                      </>
                    )}
                  </button>
                </div>
              )}

              {/* 分页信息 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 text-center">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-center gap-2 text-sm text-gray-600">
                  <div className="flex items-center justify-center">
                    <svg
                      className="w-4 h-4 mr-2 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                    显示{' '}
                    <span className="font-semibold text-gray-900">
                      {messages.length.toLocaleString()}
                    </span>{' '}
                    /{' '}
                    <span className="font-semibold text-gray-900">
                      {pagination.total.toLocaleString()}
                    </span>{' '}
                    条消息
                  </div>
                  {searchQuery && (
                    <div className="flex items-center justify-center">
                      <svg
                        className="w-4 h-4 mr-2 text-blue-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        />
                      </svg>
                      搜索结果:{' '}
                      <span className="font-semibold text-blue-600">
                        {filteredMessages.length.toLocaleString()}
                      </span>{' '}
                      条
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

// 导出默认组件
export default MessagesPage;
