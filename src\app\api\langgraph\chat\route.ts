// import { langGraphService } from '@/lib/langgraph-server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { message, threadId } = await request.json();

    if (!message) {
      return NextResponse.json({ error: '消息内容不能为空' }, { status: 400 });
    }

    // 使用 LangGraph 服务处理聊天消息
    // const sessionId =
    //   threadId ||
    //   (await langGraphService.createSession({
    //     ticker: 'CHAT', // 聊天会话使用默认ticker
    //     title: 'Chat Session',
    //     config: {},
    //   }));
    // const result = await langGraphService.sendMessage(sessionId, message);
    
    // 临时返回模拟响应
    const result = { content: 'Service temporarily disabled for build', sessionId: threadId || 'temp' };

    return NextResponse.json(result);
  } catch (error) {
    console.error('聊天处理失败:', error);
    return NextResponse.json({ error: '消息处理失败，请稍后重试' }, { status: 500 });
  }
}
