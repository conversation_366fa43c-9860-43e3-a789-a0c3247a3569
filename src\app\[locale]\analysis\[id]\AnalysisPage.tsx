'use client';

import { AnalysisOverview } from '@/components/dashboard/AnalysisOverview';
import { AnalysisProgressIndicator } from '@/components/dashboard/AnalysisProgressIndicator';
import { AnalysisReportSection } from '@/components/dashboard/AnalysisReportSection';
import { FinalDecisionSection } from '@/components/dashboard/FinalDecisionSection';
import { Breadcrumb } from '@/components/navigation/Breadcrumb';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { usePageTitle } from '@/hooks/usePageTitle';
import { useWebSocket } from '@/hooks/useWebSocket';
import useUserStore from '@/store/userStore';
import { FinalDecision } from '@/types/langgraph-database';
import {
    ClockIcon,
    ExclamationTriangleIcon,
    SignalSlashIcon,
    WifiIcon
} from '@heroicons/react/24/outline';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

// Enhanced interfaces based on the new database schema and requirements
interface AnalysisStatus {
  workflowId: string;
  taskId?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  currentStep: string;
  totalSteps: number;
  completedSteps: number;
  startTime?: string;
  endTime?: string;
  error?: string;
  stockSymbol?: string;
  analysisType?: string;
  timePeriod?: string;
}

interface AgentStatus {
  agentId: string;
  agentName: string;
  status: 'idle' | 'working' | 'completed' | 'error';
  currentTask?: string;
  progress: number;
  lastUpdate: string;
  output?: any;
  error?: string;
}

interface AnalysisReport {
  agentId: string;
  agentName: string;
  reportType: string;
  content: string;
  timestamp: string;
  confidence: number;
  keyFindings: string[];
  recommendations: string[];
  metadata?: any;
}

export default function AnalysisPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUserStore();
  const analysisId = params?.id as string;

  // State management
  const [analysisStatus, setAnalysisStatus] = useState<AnalysisStatus | null>(null);
  const [agentStatuses, setAgentStatuses] = useState<AgentStatus[]>([]);
  const [reports, setReports] = useState<AnalysisReport[]>([]);
  const [finalDecision, setFinalDecision] = useState<FinalDecision | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // WebSocket connection for real-time updates
  const {
    isConnected,
    isConnecting,
    error: wsError,
  } = useWebSocket(`/ws/analysis/${analysisId}`, {
    onMessage: (data) => {
      console.log('WebSocket message received:', data);
      handleWebSocketMessage(data);
    },
    onError: (error) => {
      console.error('WebSocket error:', error);
    },
  });

  // Set page title
  usePageTitle(
    analysisStatus?.stockSymbol
      ? `${analysisStatus.stockSymbol} 分析 - TradingAgents`
      : '分析详情 - TradingAgents'
  );

  // Handle WebSocket messages
  const handleWebSocketMessage = useCallback((data: any) => {
    try {
      const message = typeof data === 'string' ? JSON.parse(data) : data;

      switch (message.type) {
        case 'analysis_status':
          setAnalysisStatus(message.data);
          break;
        case 'agent_status':
          setAgentStatuses((prev) => {
            const updated = [...prev];
            const index = updated.findIndex((agent) => agent.agentId === message.data.agentId);
            if (index >= 0) {
              updated[index] = message.data;
            } else {
              updated.push(message.data);
            }
            return updated;
          });
          break;
        case 'analysis_report':
          setReports((prev) => [...prev, message.data]);
          break;
        case 'final_decision':
          setFinalDecision(message.data);
          break;
        case 'error':
          setError(message.data.message);
          break;
        default:
          console.log('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }, []);

  // Fetch initial analysis data
  const fetchAnalysisData = useCallback(async () => {
    if (!analysisId) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch analysis status
      const statusResponse = await fetch(`/api/langgraph/analysis/${analysisId}/status`);
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        setAnalysisStatus(statusData);
      }

      // Fetch agent statuses
      const agentsResponse = await fetch(`/api/langgraph/analysis/${analysisId}/agents`);
      if (agentsResponse.ok) {
        const agentsData = await agentsResponse.json();
        setAgentStatuses(agentsData.agents || []);
      }

      // Fetch reports
      const reportsResponse = await fetch(`/api/langgraph/analysis/${analysisId}/reports`);
      if (reportsResponse.ok) {
        const reportsData = await reportsResponse.json();
        setReports(reportsData.reports || []);
      }

      // Fetch final decision
      const decisionResponse = await fetch(`/api/langgraph/analysis/${analysisId}/decision`);
      if (decisionResponse.ok) {
        const decisionData = await decisionResponse.json();
        setFinalDecision(decisionData.decision);
      }
    } catch (error) {
      console.error('Error fetching analysis data:', error);
      setError('获取分析数据失败');
    } finally {
      setLoading(false);
    }
  }, [analysisId]);

  // Initialize data loading
  useEffect(() => {
    fetchAnalysisData();
  }, [fetchAnalysisData]);

  // Handle analysis control actions
  const handleStopAnalysis = useCallback(async () => {
    if (!analysisId) return;

    try {
      const response = await fetch(`/api/langgraph/analysis/${analysisId}/stop`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('停止分析失败');
      }

      // Refresh data
      fetchAnalysisData();
    } catch (error) {
      console.error('Error stopping analysis:', error);
      setError('停止分析失败');
    }
  }, [analysisId, fetchAnalysisData]);

  const handleRestartAnalysis = useCallback(async () => {
    if (!analysisId) return;

    try {
      const response = await fetch(`/api/langgraph/analysis/${analysisId}/restart`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('重启分析失败');
      }

      // Clear current data and refresh
      setReports([]);
      setFinalDecision(null);
      fetchAnalysisData();
    } catch (error) {
      console.error('Error restarting analysis:', error);
      setError('重启分析失败');
    }
  }, [analysisId, fetchAnalysisData]);

  // Render connection status indicator
  const renderConnectionStatus = () => {
    const statusConfig = {
      connected: { icon: WifiIcon, color: 'text-green-500', label: '已连接' },
      connecting: { icon: ClockIcon, color: 'text-yellow-500', label: '连接中' },
      disconnected: { icon: SignalSlashIcon, color: 'text-red-500', label: '已断开' },
      error: { icon: ExclamationTriangleIcon, color: 'text-red-500', label: '连接错误' },
    };

    const connectionStatus = isConnecting
      ? 'connecting'
      : isConnected
      ? 'connected'
      : 'disconnected';
    const config = statusConfig[connectionStatus] || statusConfig.disconnected;
    const Icon = config.icon;

    return (
      <div className="flex items-center space-x-2 text-sm">
        <Icon className={`h-4 w-4 ${config.color}`} />
        <span className={config.color}>{config.label}</span>
      </div>
    );
  };

  // Breadcrumb items
  const breadcrumbItems = [
    { label: '首页', href: '/' },
    { label: '任务管理', href: '/tasks' },
    { label: '分析详情', href: `/analysis/${analysisId}` },
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle className="text-red-600">错误</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">{error}</p>
            <div className="flex space-x-2">
              <Button onClick={fetchAnalysisData} variant="secondary">
                重试
              </Button>
              <Button onClick={() => router.push('/tasks')} variant="primary">
                返回任务列表
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Breadcrumb items={breadcrumbItems} />
          <div className="flex justify-between items-start mt-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {analysisStatus?.stockSymbol
                  ? `${analysisStatus.stockSymbol} 分析报告`
                  : '分析报告'}
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2">
                实时监控分析进度，查看专家报告和投资建议
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {renderConnectionStatus()}
              <div className="flex space-x-2">
                {analysisStatus?.status === 'running' && (
                  <Button onClick={handleStopAnalysis} variant="secondary" size="sm">
                    停止分析
                  </Button>
                )}
                {(analysisStatus?.status === 'completed' ||
                  analysisStatus?.status === 'failed') && (
                  <Button onClick={handleRestartAnalysis} variant="secondary" size="sm">
                    重新分析
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Overview and Progress */}
          <div className="lg:col-span-1 space-y-6">
            {/* Analysis Overview */}
            {analysisStatus && (
              <AnalysisOverview
                status={{
                  workflowId: analysisStatus?.workflowId || '',
                  taskId: analysisStatus?.taskId,
                  status: analysisStatus?.status || 'pending',
                  progress: analysisStatus?.progress || 0,
                  currentStage: analysisStatus?.currentStep || 'pending',
                  ticker: Array.isArray(params?.id) ? params.id[0] : params?.id || '',
                  title: `${Array.isArray(params?.id) ? params.id[0] : params?.id || ''} 分析`,
                  createdAt: new Date().toISOString(),
                  startedAt: undefined,
                  completedAt: undefined,
                }}
                isConnected={isConnected}
                isConnecting={isConnecting}
              />
            )}

            {/* Progress Indicator */}
            {analysisStatus && (
              <AnalysisProgressIndicator
                status={analysisStatus?.status || 'pending'}
                progress={analysisStatus?.progress || 0}
                currentStage={analysisStatus?.currentStep || 'pending'}
              />
            )}
          </div>

          {/* Right Column - Reports and Decision */}
          <div className="lg:col-span-2 space-y-6">
            {/* Analysis Reports */}
            <AnalysisReportSection analystReports={[]} researchReports={[]} />

            {/* Final Decision */}
            {finalDecision && <FinalDecisionSection decision={finalDecision} />}

            {/* Empty State */}
            {reports.length === 0 && !finalDecision && (
              <Card>
                <CardContent className="py-12 text-center">
                  <div className="text-gray-400 text-lg mb-4">
                    {analysisStatus?.status === 'running' ? '分析正在进行中...' : '暂无分析报告'}
                  </div>
                  <p className="text-gray-500">
                    {analysisStatus?.status === 'running'
                      ? '请耐心等待，专家团队正在为您生成详细的分析报告'
                      : '请启动分析任务以获取专业的投资建议'}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
