'use client';

import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useMemo } from 'react';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  const pathname = usePathname();

  // Generate breadcrumb items automatically if not provided
  const breadcrumbItems = useMemo(() => {
    if (items) return items;

    const pathSegments =
      pathname
        ?.replace(/^\/(en|zh)/, '')
        ?.split('/')
        .filter(Boolean) || [];
    const generatedItems: BreadcrumbItem[] = [{ label: '首页', href: '/' }];

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;

      let label = segment;

      // Map path segments to Chinese labels
      switch (segment) {
        case 'tasks':
          label = '任务列表';
          break;
        case 'analysis':
          label = '分析中心';
          break;
        case 'history':
          label = '分析历史';
          break;
        case 'compare':
          label = '分析对比';
          break;
        case 'create-task':
          label = '创建任务';
          break;
        default:
          // For dynamic segments like analysis IDs, try to get more context
          if (
            pathSegments[index - 1] === 'analysis' &&
            segment !== 'history' &&
            segment !== 'compare'
          ) {
            label = `分析详情 (${segment})`;
          }
          break;
      }

      generatedItems.push({
        label,
        href: isLast ? undefined : currentPath,
        current: isLast,
      });
    });

    return generatedItems;
  }, [pathname, items]);

  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <nav className={`flex ${className}`} aria-label="面包屑导航" role="navigation">
      <ol className="flex items-center space-x-2">
        {breadcrumbItems.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRightIcon
                className="h-4 w-4 text-slate-400 dark:text-slate-500 mx-2"
                aria-hidden="true"
              />
            )}

            {item.href && !item.current ? (
              <Link
                href={item.href}
                className="flex items-center text-sm font-medium text-slate-600 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                aria-current={item.current ? 'page' : undefined}
              >
                {index === 0 && <HomeIcon className="h-4 w-4 mr-1" aria-hidden="true" />}
                {item.label}
              </Link>
            ) : (
              <span
                className={`flex items-center text-sm font-medium ${
                  item.current
                    ? 'text-slate-900 dark:text-white'
                    : 'text-slate-600 dark:text-slate-400'
                }`}
                aria-current={item.current ? 'page' : undefined}
              >
                {index === 0 && <HomeIcon className="h-4 w-4 mr-1" aria-hidden="true" />}
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
