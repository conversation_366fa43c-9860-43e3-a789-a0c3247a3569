'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Mail, RefreshCw } from 'lucide-react';

interface VerificationCodeInputProps {
  email: string;
  type?: 'register' | 'reset_password' | 'change_email';
  onVerified: (code: string) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  className?: string;
}

export function VerificationCodeInput({
  email,
  type = 'register',
  onVerified,
  onError,
  disabled = false,
  className = '',
}: VerificationCodeInputProps) {
  const [code, setCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [canResend, setCanResend] = useState(true);
  
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }
    return () => clearTimeout(timer);
  }, [countdown, canResend]);

  // 发送验证码
  const sendVerificationCode = async () => {
    if (!email || isSending || countdown > 0) return;

    setIsSending(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/auth/send-verification-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          type,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setSuccess(`验证码已发送到 ${email}`);
        setCountdown(60); // 60秒倒计时
        setCanResend(false);
      } else {
        const errorMsg = data.message || '发送验证码失败';
        setError(errorMsg);
        onError?.(errorMsg);
      }
    } catch (err: any) {
      const errorMsg = '网络错误，请稍后重试';
      setError(errorMsg);
      onError?.(errorMsg);
    } finally {
      setIsSending(false);
    }
  };

  // 验证验证码
  const verifyCode = async (verificationCode: string) => {
    if (!verificationCode || verificationCode.length !== 6) return;

    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/verify-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          code: verificationCode,
          type,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setSuccess('验证码验证成功！');
        onVerified(verificationCode);
      } else {
        const errorMsg = data.message || '验证码验证失败';
        setError(errorMsg);
        onError?.(errorMsg);
        // 验证失败时清空输入
        setCode('');
        inputRefs.current.forEach(input => {
          if (input) input.value = '';
        });
        inputRefs.current[0]?.focus();
      }
    } catch (err: any) {
      const errorMsg = '网络错误，请稍后重试';
      setError(errorMsg);
      onError?.(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理单个输入框的变化
  const handleInputChange = (index: number, value: string) => {
    // 只允许数字
    const numericValue = value.replace(/\D/g, '');
    
    if (numericValue.length <= 1) {
      const newCode = code.split('');
      newCode[index] = numericValue;
      const updatedCode = newCode.join('');
      setCode(updatedCode);

      // 自动跳转到下一个输入框
      if (numericValue && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }

      // 如果输入完成，自动验证
      if (updatedCode.length === 6 && /^\d{6}$/.test(updatedCode)) {
        verifyCode(updatedCode);
      }
    }
  };

  // 处理键盘事件
  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  // 处理粘贴事件
  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').replace(/\D/g, '');
    
    if (pastedData.length === 6) {
      setCode(pastedData);
      pastedData.split('').forEach((digit, index) => {
        if (inputRefs.current[index]) {
          inputRefs.current[index]!.value = digit;
        }
      });
      verifyCode(pastedData);
    }
  };

  const typeText = {
    register: '注册',
    reset_password: '重置密码',
    change_email: '更换邮箱'
  }[type];

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="space-y-2">
        <Label htmlFor="verification-code">
          邮箱验证码
        </Label>
        <p className="text-sm text-muted-foreground">
          我们已向 <span className="font-medium">{email}</span> 发送了{typeText}验证码
        </p>
      </div>

      {/* 验证码输入框 */}
      <div className="flex justify-center space-x-2">
        {[0, 1, 2, 3, 4, 5].map((index) => (
          <Input
            key={index}
            ref={(el) => {
              inputRefs.current[index] = el;
              return undefined;
            }}
            type="text"
            inputMode="numeric"
            maxLength={1}
            className="w-12 h-12 text-center text-lg font-bold"
            disabled={disabled || isLoading}
            onChange={(e) => handleInputChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            onPaste={handlePaste}
            autoComplete="off"
          />
        ))}
      </div>

      {/* 错误信息 */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 成功信息 */}
      {success && (
        <Alert>
          <Mail className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* 重新发送按钮 */}
      <div className="flex justify-center">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          disabled={!canResend || isSending || countdown > 0 || disabled}
          onClick={sendVerificationCode}
          className="text-sm"
        >
          {isSending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              发送中...
            </>
          ) : countdown > 0 ? (
            `${countdown}秒后可重新发送`
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              重新发送验证码
            </>
          )}
        </Button>
      </div>

      {/* 提示信息 */}
      <div className="text-xs text-muted-foreground text-center space-y-1">
        <p>验证码有效期为10分钟</p>
        <p>没有收到邮件？请检查垃圾邮件文件夹</p>
      </div>
    </div>
  );
}