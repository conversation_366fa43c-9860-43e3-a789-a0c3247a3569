/**
 * 测试 parseReportContent 函数
 * 这个函数用于解析报告内容，提取结论和详情，同时保留原始内容
 */

// 模拟 parseReportContent 函数（从 AnalysisReportSection.tsx 复制）
const parseReportContent = (content) => {
  if (!content) return { conclusion: '', details: '', originalContent: '' };

  const lines = content.split('\n');
  const firstLine = lines[0] || '';

  // 尝试提取结论（通常在第一行或包含特定关键词）
  const conclusionMatch = firstLine.match(/^(买入|卖出|持有|中性|看涨|看跌|积极|消极)\s*-?\s*(.*)/);

  if (conclusionMatch) {
    return {
      conclusion: conclusionMatch[1],
      details: conclusionMatch[2] + '\n' + lines.slice(1).join('\n'),
      originalContent: content, // 保存原始内容
    };
  }

  return {
    conclusion: firstLine.length > 100 ? firstLine.substring(0, 100) + '...' : firstLine,
    details: lines.slice(1).join('\n'),
    originalContent: content, // 保存原始内容
  };
};

describe('parseReportContent 函数测试', () => {
  test('应该正确解析包含结论关键词的内容', () => {
    const content = '买入 - 基于技术分析，该股票呈现上涨趋势\n详细分析：\n1. 技术指标显示强势\n2. 成交量放大';
    const result = parseReportContent(content);
    
    expect(result.conclusion).toBe('买入');
    expect(result.details).toContain('基于技术分析');
    expect(result.details).toContain('详细分析');
    expect(result.originalContent).toBe(content);
  });

  test('应该正确处理不包含结论关键词的内容', () => {
    const content = '这是一个普通的分析报告\n包含多行内容\n没有特定的结论关键词';
    const result = parseReportContent(content);
    
    expect(result.conclusion).toBe('这是一个普通的分析报告');
    expect(result.details).toContain('包含多行内容');
    expect(result.originalContent).toBe(content);
  });

  test('应该正确处理长标题的裁剪', () => {
    // 创建一个真正超过100个字符的标题
    const longTitle = '这是一个非常长的标题，超过了100个字符的限制，应该被裁剪并添加省略号，这样用户就可以看到完整的内容通过原文功能。这个标题确实很长，包含了很多信息，需要被正确处理和裁剪。为了确保超过100个字符，我们需要添加更多的内容来测试裁剪功能是否正常工作。';
    const content = longTitle + '\n详细内容在这里';
    const result = parseReportContent(content);

    expect(result.conclusion).toContain('...');
    expect(result.conclusion.length).toBeLessThanOrEqual(103); // 100 + '...'
    expect(result.originalContent).toBe(content);
  });

  test('应该正确处理空内容', () => {
    const result = parseReportContent('');
    
    expect(result.conclusion).toBe('');
    expect(result.details).toBe('');
    expect(result.originalContent).toBe('');
  });

  test('应该正确处理只有一行的内容', () => {
    const content = '单行内容测试';
    const result = parseReportContent(content);
    
    expect(result.conclusion).toBe('单行内容测试');
    expect(result.details).toBe('');
    expect(result.originalContent).toBe(content);
  });

  test('应该正确处理各种结论关键词', () => {
    const keywords = ['买入', '卖出', '持有', '中性', '看涨', '看跌', '积极', '消极'];
    
    keywords.forEach(keyword => {
      const content = `${keyword} - 相关分析内容\n详细说明`;
      const result = parseReportContent(content);
      
      expect(result.conclusion).toBe(keyword);
      expect(result.originalContent).toBe(content);
    });
  });
});

// 运行测试的说明
console.log('测试说明：');
console.log('1. 这个测试验证了 parseReportContent 函数的核心功能');
console.log('2. 确保函数能正确提取结论、详情和保存原始内容');
console.log('3. 测试了各种边界情况，包括空内容、长标题等');
console.log('4. 验证了所有支持的结论关键词都能被正确识别');
console.log('');
console.log('运行测试命令：npm test parseReportContent.test.js');
