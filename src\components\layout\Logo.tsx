import React from 'react';
import Image from 'next/image';

export default function Logo(
  props: Omit<
    React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>,
    'ref'
  >
) {
  const { alt, width, height } = props;
  return (
    <div>
      <Image
        src="/tradingAgentLogo.png"
        alt={alt ?? 'TradingAgents Logo'}
        width={Number(width) ?? 32}
        height={Number(height) ?? 32}
      />
    </div>
  );
}
