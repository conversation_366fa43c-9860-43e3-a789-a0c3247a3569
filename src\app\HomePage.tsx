'use client';

import { EyeIcon, PlayIcon } from '@heroicons/react/24/outline';
import { motion, useScroll } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { OptimizedImage } from '@/components/seo/OptimizedImage';

export default function HomePage() {
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations('homepage');
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentScreen, setCurrentScreen] = useState(0);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start start', 'end start'],
  });

  // 双向自动滚动逻辑
  useEffect(() => {
    let isScrolling = false;
    let lastScrollY = window.scrollY;
    let lastDirection: 'up' | 'down' | null = null;

    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;

      // 判断滑动方向
      let direction: 'up' | 'down' | null = null;
      if (scrollPosition > lastScrollY) {
        direction = 'down';
      } else if (scrollPosition < lastScrollY) {
        direction = 'up';
      }
      lastScrollY = scrollPosition;
      lastDirection = direction;

      // 更新当前屏幕状态
      const screen = Math.round(scrollPosition / windowHeight);
      setCurrentScreen(screen);

      if (isScrolling) return;

      // 向下滚动：当滚动超过 25% 但小于 75% 时自动滚动到第二屏
      if (
        direction === 'down' &&
        scrollPosition > windowHeight * 0.25 &&
        scrollPosition < windowHeight * 0.75 &&
        currentScreen === 0
      ) {
        isScrolling = true;
        window.scrollTo({
          top: windowHeight,
          behavior: 'smooth',
        });
        setTimeout(() => {
          isScrolling = false;
        }, 1000);
      }
      // 向上滚动：当在第二屏但滚动位置小于 75% 时自动滚动回第一屏
      if (direction === 'up' && scrollPosition < windowHeight * 0.75 && currentScreen === 1) {
        isScrolling = true;
        window.scrollTo({
          top: 0,
          behavior: 'smooth',
        });
        setTimeout(() => {
          isScrolling = false;
          setCurrentScreen(screen);
        }, 1000);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 手动滚动到指定屏幕
  const scrollToScreen = useCallback((screenNumber: number) => {
    const windowHeight = window.innerHeight;
    window.scrollTo({
      top: screenNumber * windowHeight,
      behavior: 'smooth',
    });
  }, []);

  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowDown' && currentScreen === 0) {
        event.preventDefault();
        scrollToScreen(1);
      } else if (event.key === 'ArrowUp' && currentScreen === 1) {
        event.preventDefault();
        scrollToScreen(0);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentScreen, scrollToScreen]);

  const handleQuickStart = () => {
    router.push(`/${locale}/create-task`);
  };

  const handleViewExamples = () => {
    router.push(`/${locale}/tasks`);
  };

  return (
    <div ref={containerRef} className="relative">
      {/* 第一屏 - 首页 */}
      <section
        className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900 relative overflow-hidden"
        id="home"
        role="banner"
        aria-label="首页欢迎区域"
      >
        {/* 主体内容 */}
        <div className="flex flex-col justify-center items-center min-h-screen px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            {/* 主标题 */}
            <h1
              id="main-title"
              className="text-5xl md:text-7xl font-bold text-slate-900 dark:text-white mb-6 leading-tight"
              aria-label={t('hero.title')}
            >
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                TradingAgents
              </span>
              <br />
              {t('hero.title')}
            </h1>

            <p className="text-xl md:text-2xl text-slate-600 dark:text-slate-300 mb-8 leading-relaxed">
              {t('hero.subtitle')}
            </p>

            {/* 智能体团队展示 */}
            <div
              className="flex flex-wrap justify-center gap-4 mb-12 text-sm md:text-base"
              role="list"
              aria-label="智能体团队成员"
            >
              <div
                className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200 dark:border-slate-600"
                role="listitem"
              >
                <span className="text-blue-600 font-semibold">📊 {t('hero.analysts.market')}</span>
              </div>
              <div
                className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200 dark:border-slate-600"
                role="listitem"
              >
                <span className="text-purple-600 font-semibold">📰 {t('hero.analysts.news')}</span>
              </div>
              <div
                className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200 dark:border-slate-600"
                role="listitem"
              >
                <span className="text-green-600 font-semibold">💭 {t('hero.analysts.social')}</span>
              </div>
              <div
                className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200 dark:border-slate-600"
                role="listitem"
              >
                <span className="text-orange-600 font-semibold">📈 {t('hero.analysts.fundamental')}</span>
              </div>
              <div
                className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200 dark:border-slate-600"
                role="listitem"
              >
                <span className="text-red-600 font-semibold">🛡️ {t('hero.analysts.risk')}</span>
              </div>
            </div>

            {/* 按钮组 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <button
                onClick={handleQuickStart}
                className="group bg-blue-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center space-x-2"
                aria-label="开始创建新的分析任务"
              >
                <PlayIcon
                  className="h-5 w-5 group-hover:translate-x-1 transition-transform"
                  aria-hidden="true"
                />
                <span>{t('hero.buttons.quickStart')}</span>
              </button>

              <button
                onClick={handleViewExamples}
                className="group bg-white dark:bg-slate-800 text-slate-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-slate-200 dark:border-slate-600 hover:border-blue-600 dark:hover:border-blue-400 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center space-x-2"
                aria-label="查看现有的分析任务列表"
              >
                <EyeIcon
                  className="h-5 w-5 group-hover:scale-110 transition-transform"
                  aria-hidden="true"
                />
                <span>{t('hero.buttons.viewTasks')}</span>
              </button>
            </motion.div>
          </motion.div>

          {/* 滚动提示 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          >
            <div className="flex flex-col items-center space-y-2 text-slate-400 dark:text-slate-500">
              <span className="text-sm">{t('ui.scrollHint')}</span>
              <span className="text-xs opacity-75">{t('ui.keyboardHint')}</span>
              <motion.div
                animate={{ y: [0, 8, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-6 h-10 border-2 border-slate-300 dark:border-slate-600 rounded-full flex justify-center"
              >
                <motion.div
                  animate={{ y: [0, 12, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="w-1 h-3 bg-slate-400 dark:bg-slate-500 rounded-full mt-2"
                />
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* 第二屏预览区域 */}
        <div className="absolute bottom-0 left-0 right-0 h-1/4 bg-gradient-to-t from-slate-100 to-transparent dark:from-slate-800 dark:to-transparent opacity-50" />
      </section>

      {/* 第二屏 - 功能展示 */}
      <section
        className="min-h-screen bg-white dark:bg-slate-900 relative flex items-center justify-center"
        id="features"
        role="region"
        aria-labelledby="features-title"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2
              id="features-title"
              className="text-4xl md:text-6xl font-bold text-slate-900 dark:text-white mb-6"
            >
              {t('features.title')}
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              {t('features.subtitle')}
            </p>
          </motion.div>

          {/* 功能特性网格 */}
          <div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            role="list"
            aria-label="核心功能特性列表"
          >
            {/* 任务创建与管理 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 p-8 rounded-2xl border border-blue-200 dark:border-blue-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mb-6">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                {t('features.cards.customAnalysis.title')}
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                {t('features.cards.customAnalysis.description')}
              </p>
              <div className="text-sm text-blue-600 dark:text-blue-400">
                {t('features.cards.customAnalysis.features.flexible')}
                <br />
                {t('features.cards.customAnalysis.features.team')}
                <br />{t('features.cards.customAnalysis.features.depth')}
              </div>
            </motion.div>

            {/* 多智能体分析 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 p-8 rounded-2xl border border-purple-200 dark:border-purple-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center mb-6">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                {t('features.cards.multiAgent.title')}
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                {t('features.cards.multiAgent.description')}
              </p>
              <div className="text-sm text-purple-600 dark:text-purple-400">
                {t('features.cards.multiAgent.features.technical')}
                <br />
                {t('features.cards.multiAgent.features.news')}
                <br />
                {t('features.cards.multiAgent.features.sentiment')}
                <br />{t('features.cards.multiAgent.features.fundamental')}
              </div>
            </motion.div>

            {/* 实时分析监控 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 p-8 rounded-2xl border border-green-200 dark:border-green-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mb-6">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                {t('features.cards.realTimeTracking.title')}
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                {t('features.cards.realTimeTracking.description')}
              </p>
              <div className="text-sm text-green-600 dark:text-green-400">
                {t('features.cards.realTimeTracking.features.progress')}
                <br />
                {t('features.cards.realTimeTracking.features.discussion')}
                <br />{t('features.cards.realTimeTracking.features.visualization')}
              </div>
            </motion.div>

            {/* 消息与对话管理 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-orange-50 to-red-100 dark:from-orange-900/20 dark:to-red-900/20 p-8 rounded-2xl border border-orange-200 dark:border-orange-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-orange-600 rounded-xl flex items-center justify-center mb-6">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                {t('ui.expertDiscussion.title')}
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                {t('ui.expertDiscussion.description')}
              </p>
              <div className="text-sm text-orange-600 dark:text-orange-400">
                {t('ui.expertDiscussion.features.record')}
                <br />
                {t('ui.expertDiscussion.features.logic')}
                <br />{t('ui.expertDiscussion.features.history')}
              </div>
            </motion.div>

            {/* 数据库管理 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-cyan-50 to-blue-100 dark:from-cyan-900/20 dark:to-blue-900/20 p-8 rounded-2xl border border-cyan-200 dark:border-cyan-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-cyan-600 rounded-xl flex items-center justify-center mb-6">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                {t('ui.dataManagement.title')}
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                {t('ui.dataManagement.description')}
              </p>
              <div className="text-sm text-cyan-600 dark:text-cyan-400">
                {t('ui.dataManagement.features.archive')}
                <br />
                {t('ui.dataManagement.features.query')}
                <br />{t('ui.dataManagement.features.support')}
              </div>
            </motion.div>

            {/* LangGraph 架构 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-indigo-50 to-purple-100 dark:from-indigo-900/20 dark:to-purple-900/20 p-8 rounded-2xl border border-indigo-200 dark:border-indigo-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-indigo-600 rounded-xl flex items-center justify-center mb-6">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                {t('ui.langGraph.title')}
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                {t('ui.langGraph.description')}
              </p>
              <div className="text-sm text-indigo-600 dark:text-indigo-400">
                {t('ui.langGraph.features.models')}
                <br />
                {t('ui.langGraph.features.flexible')}
                <br />{t('ui.langGraph.features.modular')}
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}
