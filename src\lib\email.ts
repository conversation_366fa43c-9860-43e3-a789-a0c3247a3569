import nodemailer from 'nodemailer';
import { v4 as uuidv4 } from 'uuid';
import { query } from './db';

// 邮件配置接口
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

// 邮件发送结果接口
interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

// 验证码邮件模板
interface VerificationEmailData {
  email: string;
  code: string;
  type: 'register' | 'reset_password' | 'change_email';
  expiresInMinutes: number;
}

// 获取邮件配置
function getEmailConfig(): EmailConfig {
  return {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER || '',
      pass: process.env.SMTP_PASS || '',
    },
  };
}

// 创建邮件传输器
function createTransporter() {
  const config = getEmailConfig();
  
  if (!config.auth.user || !config.auth.pass) {
    throw new Error('邮件服务配置不完整，请检查环境变量');
  }

  return nodemailer.createTransport(config);
}

// 生成验证码邮件HTML模板
function generateVerificationEmailHTML(data: VerificationEmailData): string {
  const { code, type, expiresInMinutes } = data;
  
  const typeText = {
    register: '注册账户',
    reset_password: '重置密码',
    change_email: '更换邮箱'
  }[type];

  return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>邮箱验证码</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .code-box { background: white; border: 2px dashed #667eea; padding: 20px; margin: 20px 0; text-align: center; border-radius: 8px; }
            .code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 8px; font-family: 'Courier New', monospace; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; color: #666; font-size: 12px; margin-top: 30px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔐 邮箱验证码</h1>
                <p>TradingAgent 交易分析平台</p>
            </div>
            <div class="content">
                <h2>您好！</h2>
                <p>您正在进行<strong>${typeText}</strong>操作，请使用以下验证码完成验证：</p>
                
                <div class="code-box">
                    <div class="code">${code}</div>
                </div>
                
                <div class="warning">
                    <strong>⚠️ 重要提醒：</strong>
                    <ul>
                        <li>验证码有效期为 <strong>${expiresInMinutes} 分钟</strong></li>
                        <li>请勿将验证码告诉他人</li>
                        <li>如非本人操作，请忽略此邮件</li>
                    </ul>
                </div>
                
                <p>如果您没有进行此操作，请忽略此邮件。您的账户安全不会受到影响。</p>
                
                <p>感谢您使用 TradingAgent！</p>
            </div>
            <div class="footer">
                <p>此邮件由系统自动发送，请勿回复</p>
                <p>© 2025 TradingAgent. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
  `;
}

// 记录邮件发送日志
async function logEmailSend(
  email: string,
  emailType: 'verification_code' | 'welcome' | 'password_reset',
  subject: string,
  status: 'pending' | 'sent' | 'failed',
  errorMessage?: string,
  providerMessageId?: string,
  ipAddress?: string
): Promise<void> {
  try {
    const logId = uuidv4();
    await query(
      `INSERT INTO email_send_logs 
       (log_id, email, email_type, subject, status, error_message, provider_message_id, sent_at, ip_address)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        logId,
        email,
        emailType,
        subject,
        status,
        errorMessage || null,
        providerMessageId || null,
        status === 'sent' ? new Date() : null,
        ipAddress || null,
      ]
    );
  } catch (error) {
    console.error('记录邮件发送日志失败:', error);
  }
}

// 发送验证码邮件
export async function sendVerificationEmail(
  data: VerificationEmailData,
  ipAddress?: string
): Promise<EmailResult> {
  const { email, type } = data;
  
  const typeText = {
    register: '注册验证',
    reset_password: '密码重置',
    change_email: '邮箱更换'
  }[type];

  const subject = `【TradingAgent】${typeText}验证码`;

  try {
    // 记录发送开始
    await logEmailSend(email, 'verification_code', subject, 'pending', undefined, undefined, ipAddress);

    const transporter = createTransporter();
    const htmlContent = generateVerificationEmailHTML(data);

    const mailOptions = {
      from: {
        name: 'TradingAgent',
        address: process.env.SMTP_FROM || process.env.SMTP_USER || '',
      },
      to: email,
      subject,
      html: htmlContent,
    };

    const result = await transporter.sendMail(mailOptions);

    // 记录发送成功
    await logEmailSend(
      email,
      'verification_code',
      subject,
      'sent',
      undefined,
      result.messageId,
      ipAddress
    );

    return {
      success: true,
      messageId: result.messageId,
    };
  } catch (error: any) {
    console.error('发送验证码邮件失败:', error);

    // 记录发送失败
    await logEmailSend(
      email,
      'verification_code',
      subject,
      'failed',
      error.message,
      undefined,
      ipAddress
    );

    return {
      success: false,
      error: error.message || '邮件发送失败',
    };
  }
}

// 生成6位数字验证码
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 检查邮件发送频率限制
export async function checkEmailRateLimit(
  email: string,
  type: 'verification_code' | 'welcome' | 'password_reset',
  limitMinutes: number = 1
): Promise<{ allowed: boolean; waitTime?: number }> {
  try {
    const result = await query(
      `SELECT created_at FROM email_send_logs 
       WHERE email = ? AND email_type = ? AND status = 'sent'
       ORDER BY created_at DESC LIMIT 1`,
      [email, type]
    );

    const logs = result as any[];
    
    if (logs.length === 0) {
      return { allowed: true };
    }

    const lastSentTime = new Date(logs[0].created_at);
    const now = new Date();
    const timeDiff = (now.getTime() - lastSentTime.getTime()) / 1000 / 60; // 分钟

    if (timeDiff < limitMinutes) {
      const waitTime = Math.ceil(limitMinutes - timeDiff);
      return { allowed: false, waitTime };
    }

    return { allowed: true };
  } catch (error) {
    console.error('检查邮件发送频率限制失败:', error);
    return { allowed: true }; // 出错时允许发送
  }
}

// 验证邮箱格式
export function validateEmailFormat(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}