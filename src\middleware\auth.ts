import { NextRequest, NextResponse } from 'next/server';
import { verifyToken, verifyRefreshToken, generateTokens } from '@/lib/auth';

// 需要认证的路径
const protectedPaths = [
  '/api/analysis',
  '/api/tasks',
  '/api/workflows',
  '/dashboard',
  '/profile',
];

// 不需要认证的路径
const publicPaths = [
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/refresh',
  '/login',
  '/register',
  '/',
];

export async function authMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 检查是否是公开路径
  if (publicPaths.some(path => pathname.startsWith(path))) {
    return NextResponse.next();
  }

  // 检查是否是受保护的路径
  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path));
  
  if (!isProtectedPath) {
    return NextResponse.next();
  }

  // 获取 tokens
  const accessToken = request.cookies.get('access-token')?.value;
  const refreshToken = request.cookies.get('refresh-token')?.value;

  // 如果没有任何 token，重定向到登录页
  if (!accessToken && !refreshToken) {
    if (pathname.startsWith('/api/')) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // 验证 access token
  if (accessToken) {
    const payload = await verifyToken(accessToken);
    if (payload) {
      // Access token 有效，继续请求
      const response = NextResponse.next();
      response.headers.set('x-user-id', String(payload.userId));
      response.headers.set('x-user-email', String(payload.email));
      return response;
    }
  }

  // Access token 无效或不存在，尝试使用 refresh token
  if (refreshToken) {
    const refreshPayload = await verifyRefreshToken(refreshToken);
    if (refreshPayload) {
      try {
        // 生成新的 access token
        const { accessToken: newAccessToken } = await generateTokens(
          refreshPayload.userId as number,
          refreshPayload.email as string
        );

        // 创建响应并设置新的 access token
        const response = NextResponse.next();
        
        // 设置新的 access token cookie
        response.cookies.set('access-token', newAccessToken, {
          httpOnly: true,
          sameSite: 'lax',
          maxAge: 60 * 60, // 60分钟
          path: '/',
        });

        // 设置用户信息到请求头
        response.headers.set('x-user-id', String(refreshPayload.userId));
        response.headers.set('x-user-email', String(refreshPayload.email));
        response.headers.set('x-token-refreshed', 'true');

        return response;
      } catch (error) {
        console.error('Token refresh failed in middleware:', error);
      }
    }
  }

  // 所有 token 都无效，清除 cookies 并重定向
  const response = pathname.startsWith('/api/')
    ? NextResponse.json(
        { success: false, message: 'Authentication failed' },
        { status: 401 }
      )
    : NextResponse.redirect(new URL('/login', request.url));

  // 清除无效的 cookies
  response.cookies.delete('access-token');
  response.cookies.delete('refresh-token');

  return response;
}