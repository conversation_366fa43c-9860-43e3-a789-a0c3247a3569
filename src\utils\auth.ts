// 认证工具函数 - 与现有 userStore 系统兼容
import { verifyToken } from '@/lib/auth';

/**
 * 检查是否已认证（客户端）
 */
export async function isAuthenticated(): Promise<boolean> {
  if (typeof document === 'undefined') return false;

  const cookies = document.cookie.split(';');
  const accessTokenCookie = cookies.find((cookie) => cookie.trim().startsWith('access-token='));
  
  if (!accessTokenCookie) {
    return false;
  }

  const token = accessTokenCookie.split('=')[1];
  try {
    const payload = await verifyToken(token);
    return !!payload;
  } catch (error) {
    return false;
  }
}

/**
 * 获取重定向 URL 参数
 */
export function getRedirectUrl(): string | null {
  if (typeof window === 'undefined') return null;

  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('redirect');
}

/**
 * 处理登录后重定向
 */
export function handlePostLoginRedirect(defaultPath: string = '/') {
  const redirectUrl = getRedirectUrl();
  const targetUrl = redirectUrl || defaultPath;

  // 清除 URL 中的重定向参数
  if (redirectUrl) {
    const url = new URL(window.location.href);
    url.searchParams.delete('redirect');
    window.history.replaceState({}, '', url.toString());
  }

  // 跳转到目标页面
  window.location.href = targetUrl;
}

/**
 * 模拟登录（仅用于测试 middleware）
 */
export function mockLogin(role: string = 'user') {
  const mockToken = btoa(
    JSON.stringify({
      userId: 'test-user',
      role: role,
      exp: Date.now() + 24 * 60 * 60 * 1000, // 24小时后过期
    })
  );

  document.cookie = `auth-token=${mockToken}; path=/`;
}
