-- ============================================================================
-- 邮箱验证码表迁移脚本
-- 版本: 1.0
-- 创建时间: 2025-09-09
-- 说明: 为注册模块添加邮箱验证码功能支持
-- ============================================================================

USE trading_analysis;

-- ============================================================================
-- 创建邮箱验证码表
-- ============================================================================

CREATE TABLE IF NOT EXISTS email_verification_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code_id VARCHAR(36) UNIQUE NOT NULL COMMENT '验证码唯一标识 (UUID)',
    email VARCHAR(255) NOT NULL COMMENT '邮箱地址',
    code VARCHAR(6) NOT NULL COMMENT '6位数字验证码',
    code_type ENUM('register', 'reset_password', 'change_email') DEFAULT 'register' COMMENT '验证码类型',
    is_used BOOLEAN DEFAULT FALSE COMMENT '是否已使用',
    attempts INT DEFAULT 0 COMMENT '验证尝试次数',
    max_attempts INT DEFAULT 5 COMMENT '最大尝试次数',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP NULL COMMENT '使用时间',
    ip_address VARCHAR(45) COMMENT '请求IP地址',
    user_agent TEXT COMMENT '用户代理',
    INDEX idx_email (email),
    INDEX idx_code (code),
    INDEX idx_code_type (code_type),
    INDEX idx_expires_at (expires_at),
    INDEX idx_email_code_type (email, code_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱验证码表';

-- ============================================================================
-- 创建邮件发送日志表
-- ============================================================================

CREATE TABLE IF NOT EXISTS email_send_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    log_id VARCHAR(36) UNIQUE NOT NULL COMMENT '日志唯一标识 (UUID)',
    email VARCHAR(255) NOT NULL COMMENT '收件人邮箱',
    email_type ENUM('verification_code', 'welcome', 'password_reset') NOT NULL COMMENT '邮件类型',
    subject VARCHAR(255) NOT NULL COMMENT '邮件主题',
    status ENUM('pending', 'sent', 'failed') DEFAULT 'pending' COMMENT '发送状态',
    error_message TEXT COMMENT '错误信息',
    provider VARCHAR(50) COMMENT '邮件服务提供商',
    provider_message_id VARCHAR(255) COMMENT '服务商消息ID',
    sent_at TIMESTAMP NULL COMMENT '发送时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) COMMENT '请求IP地址',
    INDEX idx_email (email),
    INDEX idx_email_type (email_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件发送日志表';

-- ============================================================================
-- 创建存储过程
-- ============================================================================

-- 生成验证码
DROP PROCEDURE IF EXISTS GenerateVerificationCode;
DELIMITER //
CREATE PROCEDURE GenerateVerificationCode(
    IN p_code_id VARCHAR(36),
    IN p_email VARCHAR(255),
    IN p_code VARCHAR(6),
    IN p_code_type ENUM('register', 'reset_password', 'change_email'),
    IN p_expires_minutes INT,
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    -- 将该邮箱的同类型未使用验证码设为过期
    UPDATE email_verification_codes 
    SET is_used = TRUE, used_at = NOW()
    WHERE email = p_email 
    AND code_type = p_code_type 
    AND is_used = FALSE 
    AND expires_at > NOW();
    
    -- 插入新验证码
    INSERT INTO email_verification_codes (
        code_id, email, code, code_type, expires_at, ip_address, user_agent
    ) VALUES (
        p_code_id, p_email, p_code, p_code_type, 
        DATE_ADD(NOW(), INTERVAL p_expires_minutes MINUTE),
        p_ip_address, p_user_agent
    );
    
    COMMIT;
END //
DELIMITER ;

-- 验证验证码
DROP PROCEDURE IF EXISTS VerifyCode;
DELIMITER //
CREATE PROCEDURE VerifyCode(
    IN p_email VARCHAR(255),
    IN p_code VARCHAR(6),
    IN p_code_type ENUM('register', 'reset_password', 'change_email'),
    OUT p_result ENUM('success', 'invalid', 'expired', 'used', 'max_attempts')
)
BEGIN
    DECLARE v_count INT DEFAULT 0;
    DECLARE v_is_used BOOLEAN DEFAULT FALSE;
    DECLARE v_expires_at TIMESTAMP;
    DECLARE v_attempts INT DEFAULT 0;
    DECLARE v_max_attempts INT DEFAULT 5;
    DECLARE v_code_id VARCHAR(36);
    
    -- 查找验证码
    SELECT COUNT(*), is_used, expires_at, attempts, max_attempts, code_id
    INTO v_count, v_is_used, v_expires_at, v_attempts, v_max_attempts, v_code_id
    FROM email_verification_codes
    WHERE email = p_email 
    AND code = p_code 
    AND code_type = p_code_type
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- 验证码不存在
    IF v_count = 0 THEN
        SET p_result = 'invalid';
    -- 验证码已使用
    ELSEIF v_is_used = TRUE THEN
        SET p_result = 'used';
    -- 验证码已过期
    ELSEIF v_expires_at < NOW() THEN
        SET p_result = 'expired';
    -- 尝试次数超限
    ELSEIF v_attempts >= v_max_attempts THEN
        SET p_result = 'max_attempts';
    -- 验证成功
    ELSE
        -- 标记为已使用
        UPDATE email_verification_codes 
        SET is_used = TRUE, used_at = NOW()
        WHERE code_id = v_code_id;
        
        SET p_result = 'success';
    END IF;
    
    -- 增加尝试次数（除非已经成功）
    IF p_result != 'success' AND v_count > 0 AND v_code_id IS NOT NULL THEN
        UPDATE email_verification_codes 
        SET attempts = attempts + 1
        WHERE code_id = v_code_id;
    END IF;
END //
DELIMITER ;

-- ============================================================================
-- 创建清理过期验证码的事件
-- ============================================================================

-- 启用事件调度器
SET GLOBAL event_scheduler = ON;

-- 创建清理事件（每小时执行一次）
DROP EVENT IF EXISTS CleanExpiredVerificationCodes;
CREATE EVENT CleanExpiredVerificationCodes
ON SCHEDULE EVERY 1 HOUR
DO
  DELETE FROM email_verification_codes 
  WHERE expires_at < DATE_SUB(NOW(), INTERVAL 24 HOUR);

SELECT '邮箱验证码功能数据库迁移完成！' as status;