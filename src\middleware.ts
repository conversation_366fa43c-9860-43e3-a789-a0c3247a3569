// middleware.ts (最终修正版)

import createIntlMiddleware from 'next-intl/middleware';
import { NextRequest, NextResponse } from 'next/server';
import { seoMiddleware, shouldApplySEO, getPageLocale, cleanPathname } from '@/lib/seo/middleware';
import { applyPerformanceHeaders } from '@/lib/seo/performance-optimization';
import { verifyToken } from '@/lib/auth';

// 1. 定义常量
const protectedRoutes = ['/create-task', '/tasks', '/analysis', '/messages'];
const locales = ['zh', 'en'];
const defaultLocale = 'zh';

// 2. 创建国际化中间件实例
const intlMiddleware = createIntlMiddleware({
  locales: locales,
  defaultLocale: defaultLocale,
  localePrefix: 'always',
  alternateLinks: false,
  localeDetection: true,
});

// 4. 主中间件逻辑
export async function middleware(request: NextRequest) {
  // 排除不需要处理的路径
  const { pathname } = request.nextUrl;
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/favicon') ||
    pathname.includes('.') ||
    pathname === '/sitemap.xml' ||
    pathname === '/robots.txt'
  ) {
    return NextResponse.next();
  }

  // 首先由 intlMiddleware 处理请求
  const intlResponse = intlMiddleware(request);

  // 如果 intlMiddleware 返回了响应（重定向），直接返回
  if (intlResponse && intlResponse.status !== 200) {
    return intlResponse;
  }

  // 然后处理认证逻辑
  const accessToken = request.cookies.get('access-token')?.value;
  const payload = accessToken ? await verifyToken(accessToken) : null;
  const isAuthenticated = !!payload;

  // 移除语言前缀以进行路由保护检查
  const pathWithoutLocale = pathname.replace(/^\/(en|zh)/, '') || '/';
  const isProtectedRoute = protectedRoutes.some((route) => pathWithoutLocale.startsWith(route));
  if (isProtectedRoute && !isAuthenticated) {
    const url = request.nextUrl.clone();
    const localePrefix = pathname.startsWith('/en') ? '/en' : '/zh';
    url.pathname = `${localePrefix}/login`;
    url.searchParams.set('redirect', pathname);
    return NextResponse.redirect(url);
  }

  // 如果所有检查都通过，继续处理请求
  return intlResponse || NextResponse.next();
}

// 5. 配置 Matcher
export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
