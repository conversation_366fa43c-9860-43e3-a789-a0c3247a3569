'use client';

import React, { useState } from 'react';
import { useAuthContext } from '@/components/auth/AuthProvider';

export function AuthExample() {
  const { user, loading, error, login, logout, authenticatedFetch } = useAuthContext();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [apiResult, setApiResult] = useState<string>('');

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    const result = await login(email, password);
    if (!result.success) {
      alert(result.message || 'Login failed');
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  // 示例：使用 authenticatedFetch 调用需要认证的 API
  const callProtectedAPI = async () => {
    try {
      const response = await authenticatedFetch('/api/analysis/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskId: 'test-task-' + Date.now(),
          config: {
            ticker: 'AAPL',
          },
        }),
      });

      const data = await response.json();
      setApiResult(JSON.stringify(data, null, 2));
    } catch (error) {
      setApiResult('API call failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">认证系统示例</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {!user ? (
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">登录</h2>
          <form onSubmit={handleLogin} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">邮箱</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">密码</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <button
              type="submit"
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              登录
            </button>
          </form>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            <h2 className="text-xl font-semibold">欢迎回来！</h2>
            <p>用户ID: {user.userId}</p>
            <p>邮箱: {user.email}</p>
          </div>

          <div className="flex space-x-4">
            <button
              onClick={handleLogout}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              登出
            </button>
            <button
              onClick={callProtectedAPI}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              调用受保护的 API
            </button>
          </div>

          {apiResult && (
            <div className="bg-gray-100 p-4 rounded">
              <h3 className="font-semibold mb-2">API 调用结果:</h3>
              <pre className="text-sm overflow-x-auto">{apiResult}</pre>
            </div>
          )}
        </div>
      )}

      <div className="bg-blue-50 p-4 rounded">
        <h3 className="font-semibold mb-2">认证系统特性:</h3>
        <ul className="text-sm space-y-1">
          <li>✅ 自动 Token 刷新 (Access Token 过期时自动使用 Refresh Token)</li>
          <li>✅ 服务端中间件自动处理认证</li>
          <li>✅ 客户端自动重试机制</li>
          <li>✅ 定时刷新 Token (每 50 分钟)</li>
          <li>✅ 安全的 HttpOnly Cookies</li>
          <li>✅ 完整的错误处理</li>
        </ul>
      </div>
    </div>
  );
}