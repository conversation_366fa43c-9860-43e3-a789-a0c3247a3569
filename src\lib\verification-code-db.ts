import { query } from './db';
import { v4 as uuidv4 } from 'uuid';

// 验证码类型
export type CodeType = 'register' | 'reset_password' | 'change_email';

// 验证结果类型
export type VerifyResult = 'success' | 'invalid' | 'expired' | 'used' | 'max_attempts';

// 验证码记录接口
export interface VerificationCode {
  id: number;
  code_id: string;
  email: string;
  code: string;
  code_type: CodeType;
  is_used: boolean;
  attempts: number;
  max_attempts: number;
  expires_at: Date;
  created_at: Date;
  used_at?: Date;
  ip_address?: string;
  user_agent?: string;
}

// 生成验证码记录
export async function createVerificationCode(
  email: string,
  code: string,
  codeType: CodeType,
  expiresInMinutes: number = 10,
  ipAddress?: string,
  userAgent?: string
): Promise<string> {
  try {
    const codeId = uuidv4();
    
    // 调用存储过程生成验证码
    await query(
      'CALL GenerateVerificationCode(?, ?, ?, ?, ?, ?, ?)',
      [codeId, email, code, codeType, expiresInMinutes, ipAddress || null, userAgent || null]
    );
    
    return codeId;
  } catch (error: any) {
    console.error('创建验证码失败:', error);
    throw new Error('创建验证码失败');
  }
}

// 验证验证码
export async function verifyVerificationCode(
  email: string,
  code: string,
  codeType: CodeType
): Promise<VerifyResult> {
  try {
    // 调用存储过程验证验证码
    const result = await query(
      'CALL VerifyCode(?, ?, ?, @result); SELECT @result as result;',
      [email, code, codeType]
    );
    
    // 获取结果
    const resultArray = result as any[];
    const verifyResult = resultArray[1]?.[0]?.result as VerifyResult;
    
    return verifyResult || 'invalid';
  } catch (error: any) {
    console.error('验证验证码失败:', error);
    return 'invalid';
  }
}

// 获取用户最近的验证码记录
export async function getRecentVerificationCode(
  email: string,
  codeType: CodeType,
  limitMinutes: number = 60
): Promise<VerificationCode | null> {
  try {
    const result = await query(
      `SELECT * FROM email_verification_codes 
       WHERE email = ? AND code_type = ? 
       AND created_at > DATE_SUB(NOW(), INTERVAL ? MINUTE)
       ORDER BY created_at DESC LIMIT 1`,
      [email, codeType, limitMinutes]
    );
    
    const codes = result as VerificationCode[];
    return codes.length > 0 ? codes[0] : null;
  } catch (error: any) {
    console.error('获取验证码记录失败:', error);
    return null;
  }
}

// 检查验证码发送频率限制
export async function checkVerificationCodeRateLimit(
  email: string,
  codeType: CodeType,
  limitMinutes: number = 1
): Promise<{ allowed: boolean; waitTime?: number; lastSentTime?: Date }> {
  try {
    const result = await query(
      `SELECT created_at FROM email_verification_codes 
       WHERE email = ? AND code_type = ?
       ORDER BY created_at DESC LIMIT 1`,
      [email, codeType]
    );
    
    const codes = result as any[];
    
    if (codes.length === 0) {
      return { allowed: true };
    }
    
    const lastSentTime = new Date(codes[0].created_at);
    const now = new Date();
    const timeDiff = (now.getTime() - lastSentTime.getTime()) / 1000 / 60; // 分钟
    
    if (timeDiff < limitMinutes) {
      const waitTime = Math.ceil(limitMinutes - timeDiff);
      return { allowed: false, waitTime, lastSentTime };
    }
    
    return { allowed: true, lastSentTime };
  } catch (error: any) {
    console.error('检查验证码发送频率限制失败:', error);
    return { allowed: true }; // 出错时允许发送
  }
}

// 获取验证码统计信息
export async function getVerificationCodeStats(
  email: string,
  codeType: CodeType,
  hours: number = 24
): Promise<{
  totalSent: number;
  totalUsed: number;
  totalExpired: number;
  recentAttempts: number;
}> {
  try {
    const result = await query(
      `SELECT 
         COUNT(*) as total_sent,
         SUM(CASE WHEN is_used = TRUE AND used_at IS NOT NULL THEN 1 ELSE 0 END) as total_used,
         SUM(CASE WHEN expires_at < NOW() AND is_used = FALSE THEN 1 ELSE 0 END) as total_expired,
         SUM(attempts) as recent_attempts
       FROM email_verification_codes 
       WHERE email = ? AND code_type = ? 
       AND created_at > DATE_SUB(NOW(), INTERVAL ? HOUR)`,
      [email, codeType, hours]
    );
    
    const stats = result as any[];
    const row = stats[0] || {};
    
    return {
      totalSent: parseInt(row.total_sent) || 0,
      totalUsed: parseInt(row.total_used) || 0,
      totalExpired: parseInt(row.total_expired) || 0,
      recentAttempts: parseInt(row.recent_attempts) || 0,
    };
  } catch (error: any) {
    console.error('获取验证码统计信息失败:', error);
    return {
      totalSent: 0,
      totalUsed: 0,
      totalExpired: 0,
      recentAttempts: 0,
    };
  }
}

// 清理过期验证码
export async function cleanExpiredVerificationCodes(olderThanHours: number = 24): Promise<number> {
  try {
    const result = await query(
      `DELETE FROM email_verification_codes 
       WHERE expires_at < DATE_SUB(NOW(), INTERVAL ? HOUR)`,
      [olderThanHours]
    );
    
    const deleteResult = result as any;
    return deleteResult.affectedRows || 0;
  } catch (error: any) {
    console.error('清理过期验证码失败:', error);
    return 0;
  }
}

// 使验证码失效（用于用户主动取消等场景）
export async function invalidateVerificationCode(
  email: string,
  codeType: CodeType
): Promise<boolean> {
  try {
    await query(
      `UPDATE email_verification_codes 
       SET is_used = TRUE, used_at = NOW()
       WHERE email = ? AND code_type = ? AND is_used = FALSE AND expires_at > NOW()`,
      [email, codeType]
    );
    
    return true;
  } catch (error: any) {
    console.error('使验证码失效失败:', error);
    return false;
  }
}

// 检查邮箱是否存在有效验证码
export async function hasValidVerificationCode(
  email: string,
  codeType: CodeType
): Promise<boolean> {
  try {
    const result = await query(
      `SELECT COUNT(*) as count FROM email_verification_codes 
       WHERE email = ? AND code_type = ? AND is_used = FALSE AND expires_at > NOW()`,
      [email, codeType]
    );
    
    const counts = result as any[];
    return (counts[0]?.count || 0) > 0;
  } catch (error: any) {
    console.error('检查有效验证码失败:', error);
    return false;
  }
}