import Script from 'next/script';

export function StructuredData() {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: 'TradingAgents',
    applicationCategory: 'FinanceApplication',
    description:
      '专业的AI驱动股票分析平台，基于多智能体大语言模型技术，提供多维度智能分析，为投资决策提供全方位的数据支撑和智能建议。',
    url: 'https://tradingagent.top',
    image: 'https://tradingagent.top/tradingAgentLogoWithBg.png',
    author: {
      '@type': 'Organization',
      name: 'TradingAgents Team',
      url: 'https://tradingagent.top',
    },
    publisher: {
      '@type': 'Organization',
      name: 'TradingAgents',
      logo: {
        '@type': 'ImageObject',
        url: 'https://tradingagent.top/tradingAgentLogo.png',
        width: 512,
        height: 512,
      },
    },
    operatingSystem: 'Web',
    softwareVersion: '1.0.0',
    datePublished: '2024-01-01',
    dateModified: new Date().toISOString().split('T')[0],
    features: [
      'AI智能股票分析',
      '多智能体协作分析',
      '市场情绪分析',
      '技术面分析',
      '基本面分析',
      '新闻情报分析',
      '社交媒体分析',
      '风险管理',
      '投资决策支持',
    ],
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'CNY',
      availability: 'https://schema.org/InStock',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      ratingCount: '156',
      bestRating: '5',
      worstRating: '1',
    },
  };

  const organizationData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'TradingAgents',
    url: 'https://tradingagent.top',
    logo: 'https://tradingagent.top/tradingAgentLogo.png',
    description: '专业的AI驱动股票分析平台',
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'CN',
    },
    sameAs: ['https://github.com/your-github', 'https://twitter.com/TradingAgents'],
  };

  const websiteData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'TradingAgents',
    url: 'https://tradingagent.top',
    description: 'AI智能股票分析平台',
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://tradingagent.top/search?q={search_term_string}',
      'query-input': 'required name=search_term_string',
    },
  };

  return (
    <>
      <Script
        id="structured-data-software"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <Script
        id="structured-data-organization"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationData),
        }}
      />
      <Script
        id="structured-data-website"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteData),
        }}
      />
    </>
  );
}
