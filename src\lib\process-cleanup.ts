// 全局进程清理处理器
// 确保在进程退出时正确清理所有资源和事件监听器

import { cleanupAbortSignal } from './abort-signal-manager';

let isCleanupRegistered = false;

export const registerProcessCleanup = (): void => {
  if (isCleanupRegistered || process.env.NODE_ENV === 'test') {
    return;
  }

  const cleanup = async (signal: string) => {
    console.log(`收到 ${signal} 信号，开始清理资源...`);

    try {
      // 动态导入清理函数避免循环依赖
      const analysisModule = await import('./analysis-service');
      const langGraphModule = await import('./langgraph-server');

      // 清理分析服务
      if (analysisModule.cleanupAnalysisService) {
        analysisModule.cleanupAnalysisService();
      }

      // 清理 LangGraph 服务
      if (langGraphModule.cleanupLangGraphService) {
        langGraphModule.cleanupLangGraphService();
      }

      // 清理 AbortSignal 管理器
      cleanupAbortSignal();

      console.log('资源清理完成');
    } catch (error) {
      console.error('清理资源时出错:', error);
    }

    process.exit(0);
  };

  // 注册进程退出处理器 - 使用 once 避免重复监听
  process.once('SIGINT', () => cleanup('SIGINT'));
  process.once('SIGTERM', () => cleanup('SIGTERM'));
  process.once('SIGQUIT', () => cleanup('SIGQUIT'));

  // 处理未捕获的异常 - 使用 once 避免重复监听
  process.once('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    cleanup('uncaughtException');
  });

  process.once('unhandledRejection', (reason, promise) => {
    console.error('未处理的 Promise 拒绝:', reason, 'at:', promise);
    cleanup('unhandledRejection');
  });

  isCleanupRegistered = true;
  console.log('进程清理处理器已注册');
};

// 自动注册清理处理器
registerProcessCleanup();
