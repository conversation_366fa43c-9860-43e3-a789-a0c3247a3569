'use client';

import { useState, useEffect, useCallback } from 'react';

interface User {
  userId: number;
  email: string;
}

interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null,
  });

  // 刷新 token
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAuthState(prev => ({
            ...prev,
            user: data.user,
            error: null,
          }));
          return true;
        }
      }
      
      // 刷新失败，清除用户状态
      setAuthState(prev => ({
        ...prev,
        user: null,
        error: 'Session expired',
      }));
      return false;
    } catch (error) {
      console.error('Token refresh failed:', error);
      setAuthState(prev => ({
        ...prev,
        user: null,
        error: 'Network error',
      }));
      return false;
    }
  }, []);

  // 带自动重试的 API 请求
  const authenticatedFetch = useCallback(async (
    url: string,
    options: RequestInit = {}
  ): Promise<Response> => {
    const makeRequest = async (): Promise<Response> => {
      return fetch(url, {
        ...options,
        credentials: 'include',
      });
    };

    let response = await makeRequest();

    // 如果返回 401，尝试刷新 token 后重试
    if (response.status === 401) {
      const refreshSuccess = await refreshToken();
      if (refreshSuccess) {
        // 刷新成功，重试原请求
        response = await makeRequest();
      }
    }

    return response;
  }, [refreshToken]);

  // 检查当前用户状态
  const checkAuth = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }));
      
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAuthState({
            user: data.user,
            loading: false,
            error: null,
          });
          return;
        }
      }

      // 如果获取用户信息失败，尝试刷新 token
      const refreshSuccess = await refreshToken();
      if (!refreshSuccess) {
        setAuthState({
          user: null,
          loading: false,
          error: null,
        });
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setAuthState({
        user: null,
        loading: false,
        error: 'Network error',
      });
    }
  }, [refreshToken]);

  // 登录
  const login = useCallback(async (email: string, password: string) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }));

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setAuthState({
          user: data.user,
          loading: false,
          error: null,
        });
        return { success: true };
      } else {
        setAuthState(prev => ({
          ...prev,
          loading: false,
          error: data.message || 'Login failed',
        }));
        return { success: false, message: data.message };
      }
    } catch (error) {
      const errorMessage = 'Network error';
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      return { success: false, message: errorMessage };
    }
  }, []);

  // 登出
  const logout = useCallback(async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      setAuthState({
        user: null,
        loading: false,
        error: null,
      });
    }
  }, []);

  // 组件挂载时检查认证状态
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // 设置定时刷新 token（可选）
  useEffect(() => {
    if (!authState.user) return;

    // 每 50 分钟刷新一次 token（access token 有效期 60 分钟）
    const interval = setInterval(() => {
      refreshToken();
    }, 50 * 60 * 1000);

    return () => clearInterval(interval);
  }, [authState.user, refreshToken]);

  return {
    user: authState.user,
    loading: authState.loading,
    error: authState.error,
    login,
    logout,
    refreshToken,
    authenticatedFetch,
    checkAuth,
  };
}