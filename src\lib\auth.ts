import bcrypt from 'bcryptjs';
import { de } from 'date-fns/locale';
import { SignJWT, jwtVerify } from 'jose';
import { cookies } from 'next/headers';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-this-in-production';
const REFRESH_SECRET =
  process.env.REFRESH_SECRET || 'your-refresh-secret-change-this-in-production';

// 密码加密
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

// 验证密码
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// 生成JWT token
export async function generateTokens(userId: number, email: string) {
  const payload = { userId, email };

  // 访问token (60分钟)
  const accessToken = await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('60m')
    .setIssuedAt()
    .sign(new TextEncoder().encode(JWT_SECRET));

  // 刷新token (7天)
  const refreshToken = await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('7d')
    .setIssuedAt()
    .sign(new TextEncoder().encode(REFRESH_SECRET));

  return { accessToken, refreshToken };
}

// 验证JWT token
export async function verifyToken(token: string) {
  try {
    const { payload } = await jwtVerify(token, new TextEncoder().encode(JWT_SECRET));
    return payload;
  } catch (error) {
    return null;
  }
}

// 验证刷新token
export async function verifyRefreshToken(token: string) {
  try {
    const { payload } = await jwtVerify(token, new TextEncoder().encode(REFRESH_SECRET));
    return payload;
  } catch (error) {
    return null;
  }
}

// 设置认证cookie
export async function setAuthCookies(accessToken: string, refreshToken: string) {
  const cookieStore = await cookies();

  cookieStore.set('access-token', accessToken, {
    httpOnly: true,
    // secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60, // 15分钟
    path: '/',
  });

  cookieStore.set('refresh-token', refreshToken, {
    httpOnly: true,
    // secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 7 * 24 * 60 * 60, // 7天
    path: '/',
  });
}

// 清除认证cookie
export async function clearAuthCookies() {
  const cookieStore = await cookies();

  cookieStore.delete('access-token');
  cookieStore.delete('refresh-token');
}

// 刷新访问令牌
export async function refreshAccessToken() {
  const cookieStore = await cookies();
  const refreshToken = cookieStore.get('refresh-token')?.value;

  if (!refreshToken) {
    throw new Error('No refresh token found');
  }

  const payload = await verifyRefreshToken(refreshToken);
  if (!payload) {
    throw new Error('Invalid refresh token');
  }

  // 生成新的访问令牌
  const { accessToken } = await generateTokens(payload.userId as number, payload.email as string);

  // 更新访问令牌 cookie
  cookieStore.set('access-token', accessToken, {
    httpOnly: true,
    // secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60, // 60分钟
    path: '/',
  });

  return {
    userId: payload.userId as number,
    email: payload.email as string,
  };
}

// 获取当前用户（带自动刷新功能）
export async function getCurrentUser() {
  const cookieStore = await cookies();
  const token = cookieStore.get('access-token')?.value;

  if (!token) {
    // 没有访问令牌，尝试使用刷新令牌
    try {
      return await refreshAccessToken();
    } catch (error) {
      return null;
    }
  }

  const payload = await verifyToken(token);
  if (!payload) {
    // 访问令牌无效，尝试刷新
    try {
      return await refreshAccessToken();
    } catch (error) {
      return null;
    }
  }

  return {
    userId: payload.userId as number,
    email: payload.email as string,
  };
}

// 验证邮箱格式
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 验证密码强度
export function validatePassword(password: string): { valid: boolean; message?: string } {
  if (password.length < 8) {
    return { valid: false, message: '密码长度至少8位' };
  }

  if (!/[A-Z]/.test(password)) {
    return { valid: false, message: '密码必须包含大写字母' };
  }

  if (!/[a-z]/.test(password)) {
    return { valid: false, message: '密码必须包含小写字母' };
  }

  if (!/[0-9]/.test(password)) {
    return { valid: false, message: '密码必须包含数字' };
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    return { valid: false, message: '密码必须包含特殊字符' };
  }

  return { valid: true };
}
