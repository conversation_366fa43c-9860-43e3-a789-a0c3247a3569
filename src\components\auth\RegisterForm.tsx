'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { VerificationCodeInput } from './VerificationCodeInput';
import { Loader2, Eye, EyeOff, CheckCircle, XCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Label } from '../ui/label';
import { Alert, AlertDescription } from '../ui/alert';

interface RegisterFormProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

interface FormData {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
}

interface PasswordStrength {
  score: number;
  feedback: string[];
  isValid: boolean;
}

export function RegisterForm({ onSuccess, onError }: RegisterFormProps) {
  const router = useRouter();
  const [step, setStep] = useState<'form' | 'verification'>('form');
  const [formData, setFormData] = useState<FormData>({
    email: '',
    username: '',
    password: '',
    confirmPassword: '',
  });
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>({
    score: 0,
    feedback: [],
    isValid: false,
  });

  // 验证邮箱格式
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // 检查密码强度
  const checkPasswordStrength = (password: string): PasswordStrength => {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('密码长度至少8位');
    }

    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('包含小写字母');
    }

    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('包含大写字母');
    }

    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('包含数字');
    }

    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    } else {
      feedback.push('包含特殊字符');
    }

    return {
      score,
      feedback,
      isValid: score >= 4,
    };
  };

  // 处理表单输入变化
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError('');

    // 实时检查密码强度
    if (field === 'password') {
      setPasswordStrength(checkPasswordStrength(value));
    }
  };

  // 发送验证码
  const sendVerificationCode = async () => {
    // 验证表单
    if (!formData.email || !formData.username || !formData.password || !formData.confirmPassword) {
      setError('请填写所有必填字段');
      return;
    }

    if (!validateEmail(formData.email)) {
      setError('请输入有效的邮箱地址');
      return;
    }

    if (formData.username.length < 2 || formData.username.length > 50) {
      setError('用户名长度必须在2-50个字符之间');
      return;
    }

    if (!passwordStrength.isValid) {
      setError('密码强度不够，请按照提示设置更强的密码');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/send-verification-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          type: 'register',
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setStep('verification');
      } else {
        const errorMsg = data.message || '发送验证码失败';
        setError(errorMsg);
        onError?.(errorMsg);
      }
    } catch (err: any) {
      const errorMsg = '网络错误，请稍后重试';
      setError(errorMsg);
      onError?.(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  // 完成注册
  const handleRegister = async (code: string) => {
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          username: formData.username,
          password: formData.password,
          verificationCode: code,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        onSuccess?.();
        router.push('/dashboard');
      } else {
        const errorMsg = data.message || '注册失败';
        setError(errorMsg);
        onError?.(errorMsg);
      }
    } catch (err: any) {
      const errorMsg = '网络错误，请稍后重试';
      setError(errorMsg);
      onError?.(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  // 返回表单步骤
  const goBackToForm = () => {
    setStep('form');
    setVerificationCode('');
    setError('');
  };

  // 密码强度颜色
  const getPasswordStrengthColor = (score: number) => {
    if (score < 2) return 'bg-red-500';
    if (score < 4) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // 密码强度文本
  const getPasswordStrengthText = (score: number) => {
    if (score < 2) return '弱';
    if (score < 4) return '中';
    return '强';
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>注册账户</CardTitle>
        <CardDescription>
          {step === 'form' ? '创建您的 TradingAgent 账户' : '验证您的邮箱地址'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {step === 'form' ? (
          <>
            {/* 邮箱输入 */}
            <div className="space-y-2">
              <Label htmlFor="email">邮箱地址 *</Label>
              <Input
                id="email"
                type="email"
                placeholder="请输入邮箱地址"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={isLoading}
                required
              />
            </div>

            {/* 用户名输入 */}
            <div className="space-y-2">
              <Label htmlFor="username">用户名 *</Label>
              <Input
                id="username"
                type="text"
                placeholder="请输入用户名 (2-50个字符)"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                disabled={isLoading}
                required
              />
            </div>

            {/* 密码输入 */}
            <div className="space-y-2">
              <Label htmlFor="password">密码 *</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="请输入密码"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  disabled={isLoading}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {/* 密码强度指示器 */}
              {formData.password && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor(passwordStrength.score)}`}
                        style={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium">
                      {getPasswordStrengthText(passwordStrength.score)}
                    </span>
                  </div>
                  {passwordStrength.feedback.length > 0 && (
                    <div className="text-xs text-muted-foreground">
                      <p>密码需要包含：</p>
                      <ul className="list-disc list-inside space-y-1">
                        {passwordStrength.feedback.map((item, index) => (
                          <li key={index}>{item}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 确认密码输入 */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">确认密码 *</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="请再次输入密码"
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  disabled={isLoading}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={isLoading}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {formData.confirmPassword && (
                <div className="flex items-center space-x-2 text-xs">
                  {formData.password === formData.confirmPassword ? (
                    <>
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span className="text-green-600">密码匹配</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="h-3 w-3 text-red-500" />
                      <span className="text-red-600">密码不匹配</span>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* 错误信息 */}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* 发送验证码按钮 */}
            <Button
              type="button"
              className="w-full"
              onClick={sendVerificationCode}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  发送中...
                </>
              ) : (
                '发送验证码'
              )}
            </Button>
          </>
        ) : (
          <>
            {/* 验证码输入 */}
            <VerificationCodeInput
              email={formData.email}
              type="register"
              onVerified={handleRegister}
              onError={(err) => setError(err)}
              disabled={isLoading}
            />

            {/* 错误信息 */}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* 返回按钮 */}
            <Button
              type="button"
              variant="secondary"
              className="w-full"
              onClick={goBackToForm}
              disabled={isLoading}
            >
              返回修改信息
            </Button>
          </>
        )}

        {/* 登录链接 */}
        <div className="text-center text-sm text-muted-foreground">
          已有账户？{' '}
          <Button
            variant="ghost"
            className="p-0 h-auto font-normal"
            onClick={() => router.push('/login')}
          >
            立即登录
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}