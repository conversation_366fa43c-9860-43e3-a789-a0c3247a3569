###
 # @Author: e<PERSON><PERSON><PERSON> <EMAIL>
 # @Date: 2025-07-21 20:21:41
 # @LastEditors: ezrealbb <EMAIL>
 # @LastEditTime: 2025-08-17 19:53:42
 # @FilePath: \trading-agents-frontend\.env.production
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 
# 生产环境配置
NODE_ENV=production

# API配置 - 请根据实际情况修改
NEXT_PUBLIC_BASE_URL=https://tradingagent.top
NEXT_PUBLIC_API_BASE_URL=https://tradingagent.top
NEXT_PUBLIC_API_BACKEND_BASE_URL=http://tradingagents-akshare-backend:5000
NEXT_PUBLIC_WS_URL=ws://************:8000
BACK_END_URL=http://tradingagents-akshare-backend:5000
FRONTEND_URL=http://localhost:3000
NEXT_PUBLIC_GA_ID=G-WQ1ZPHQT63

# 第三方API密钥 - 请填入实际的密钥
NEXT_PUBLIC_OPENAI_API_KEY=sk-nBa5UdWvrDVYgsMYKhctz0BRK9l5JLuRzlNR7KJSyyWvFNqS
NEXT_PUBLIC_FINNHUB_API_KEY=your-finnhub-api-key

# 数据库配置
MYSQL_ROOT_PASSWORD=trading123
MYSQL_DATABASE=trading_analysis
MYSQL_USER=trading_user
MYSQL_PASSWORD=trading123

# 生产环境特定配置
NEXT_TELEMETRY_DISABLED=1

# LangSmith配置
LANGSMITH_TRACING="true"
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_API_KEY="***************************************************"
LANGSMITH_PROJECT="tradingagent"

# 邮件服务配置 (用于验证码发送)
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=**************************************************
