import { NextRequest, NextResponse } from 'next/server';
import { refreshAccessToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const user = await refreshAccessToken();
    
    return NextResponse.json({
      success: true,
      message: 'Token refreshed successfully',
      user: {
        userId: user.userId,
        email: user.email,
      },
    });
  } catch (error) {
    console.error('Token refresh failed:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : 'Token refresh failed',
      },
      { status: 401 }
    );
  }
}