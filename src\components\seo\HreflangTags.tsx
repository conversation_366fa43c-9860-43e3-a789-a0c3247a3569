import { useRouter } from 'next/router';
import Head from 'next/head';

interface HreflangProps {
  currentPath?: string;
}

export function HreflangTags({ currentPath = '' }: HreflangProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://tradingagent.top';

  // Remove /en prefix from current path for Chinese version
  const chinesePath = currentPath.startsWith('/en') ? currentPath.replace('/en', '') : currentPath;
  const englishPath = currentPath.startsWith('/en') ? currentPath : `/en${currentPath}`;

  return (
    <Head>
      {/* Hreflang tags for language versions */}
      <link rel="alternate" hrefLang="zh-CN" href={`${baseUrl}${chinesePath || '/'}`} />
      <link rel="alternate" hrefLang="en-US" href={`${baseUrl}${englishPath}`} />
      <link rel="alternate" hrefLang="en" href={`${baseUrl}${englishPath}`} />
      <link rel="alternate" hrefLang="x-default" href={`${baseUrl}${chinesePath || '/'}`} />

      {/* Language-specific canonical URLs */}
      {currentPath.startsWith('/en') ? (
        <link rel="canonical" href={`${baseUrl}${englishPath}`} />
      ) : (
        <link rel="canonical" href={`${baseUrl}${chinesePath || '/'}`} />
      )}
    </Head>
  );
}

// Hook for easy hreflang usage
export function useHreflang(currentPath?: string) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://tradingagent.top';

  const getAlternateUrls = (path: string = '') => {
    const chinesePath = path.startsWith('/en') ? path.replace('/en', '') : path;
    const englishPath = path.startsWith('/en') ? path : `/en${path}`;

    return {
      'zh-CN': `${baseUrl}${chinesePath || '/'}`,
      'en-US': `${baseUrl}${englishPath}`,
      en: `${baseUrl}${englishPath}`,
      'x-default': `${baseUrl}${chinesePath || '/'}`,
    };
  };

  return { getAlternateUrls };
}
