'use client';

import { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline';
import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';

export function LanguageSwitcher() {
  const router = useRouter();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  // Determine current language
  const isEnglish = pathname?.startsWith('/en') || false;
  const currentLang = isEnglish ? 'en' : 'zh';

  const languages = [
    {
      code: 'zh',
      name: '中文',
      flag: '🇨🇳',
      path: isEnglish ? pathname?.replace('/en', '') || '/' : pathname || '/',
    },
    {
      code: 'en',
      name: 'English',
      flag: '🇺🇸',
      path: isEnglish ? pathname || '/' : `/en${pathname || '/'}`,
    },
  ];

  const handleLanguageChange = (langPath: string) => {
    router.push(langPath);
    setIsOpen(false);
  };

  const currentLanguage = languages.find((lang) => lang.code === currentLang);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-600 hover:border-blue-500 dark:hover:border-blue-400 transition-colors"
        aria-label="Change language"
      >
        <GlobeAltIcon className="h-4 w-4 text-slate-600 dark:text-slate-300" />
        <span className="text-sm font-medium text-slate-700 dark:text-slate-200">
          {currentLanguage?.flag} {currentLanguage?.name}
        </span>
        <ChevronDownIcon
          className={`h-4 w-4 text-slate-600 dark:text-slate-300 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div className="fixed inset-0 z-10" onClick={() => setIsOpen(false)} />

          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-slate-200 dark:border-slate-600 z-20">
            <div className="py-1">
              {languages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => handleLanguageChange(language.path)}
                  className={`w-full text-left px-4 py-2 text-sm flex items-center space-x-3 hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors ${
                    language.code === currentLang
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'text-slate-700 dark:text-slate-200'
                  }`}
                >
                  <span className="text-lg">{language.flag}</span>
                  <span className="font-medium">{language.name}</span>
                  {language.code === currentLang && (
                    <span className="ml-auto">
                      <svg
                        className="h-4 w-4 text-blue-600"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </span>
                  )}
                </button>
              ))}
            </div>

            {/* Language info */}
            <div className="border-t border-slate-200 dark:border-slate-600 px-4 py-2">
              <p className="text-xs text-slate-500 dark:text-slate-400">
                {isEnglish
                  ? 'Switch to Chinese for better local experience'
                  : 'Switch to English for global audience'}
              </p>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

// Simple text-only language switcher for minimal UI
export function SimpleLanguageSwitcher() {
  const router = useRouter();
  const pathname = usePathname();

  const isEnglish = pathname?.startsWith('/en') || false;

  const handleToggle = () => {
    if (isEnglish) {
      // Switch to Chinese
      const chinesePath = pathname?.replace('/en', '') || '/';
      router.push(chinesePath);
    } else {
      // Switch to English
      const englishPath = `/en${pathname}`;
      router.push(englishPath);
    }
  };

  return (
    <button
      onClick={handleToggle}
      className="text-sm text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium"
      aria-label={isEnglish ? 'Switch to Chinese' : 'Switch to English'}
    >
      {isEnglish ? '中文' : 'English'}
    </button>
  );
}
