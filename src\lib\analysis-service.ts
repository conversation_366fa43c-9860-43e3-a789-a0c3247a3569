// Analysis Service - Core Logic for Task-based Analysis Execution
// Integrates LangGraph service with database operations and provides
// comprehensive analysis execution, monitoring, and error handling

import { EventEmitter } from 'events';
import {
  CompleteWorkflowStatus,
  CreateCompleteWorkflowRequest,
  WorkflowStatus,
} from '../types/langgraph-database';
import { EnhancedLangGraphDatabase } from './enhanced-langgraph-database';
import { langGraphService } from './langgraph-server';
import { taskFlowDb } from './task-flow-database';

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface ExecuteAnalysisRequest {
  taskId: string;
  config?: any;
}

export interface AnalysisResult {
  workflowId: string;
  taskId: string;
  status: 'started' | 'completed' | 'failed';
  message: string;
}

export interface Task {
  task_id: string;
  ticker: string;
  title: string;
  description?: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  config?: any;
  created_by: string;
  created_at: string;
  workflow_id?: string;
}

export interface AnalysisStatus {
  workflowId: string;
  taskId: string;
  status: WorkflowStatus;
  progress: number;
  currentStage: string;
  ticker: string;
  title: string;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  analystReports: any[];
  researchReports: any[];
  finalDecision: any;
  recentEvents: any[];
  error?: string;
}

export class AnalysisError extends Error {
  constructor(
    message: string,
    public code: string = 'ANALYSIS_ERROR',
    public statusCode: number = 500
  ) {
    super(message);
    this.name = 'AnalysisError';
  }
}

// ============================================================================
// Analysis Service Class
// ============================================================================

export class AnalysisService extends EventEmitter {
  private activeAnalyses: Map<string, { workflowId: string; taskId: string }> = new Map();
  private sessionUpdateHandler?: (data: any) => void;

  constructor() {
    super();
    // 设置最大监听器数量，避免内存泄漏警告
    this.setMaxListeners(20);
    this.setupEventHandlers();
    this.recoverInterruptedAnalyses();
  }

  // ============================================================================
  // Core Analysis Execution
  // ============================================================================

  /**
   * Execute analysis based on existing task
   * Requirements: 需求 1.1, 需求 1.4, 需求 1.5, 需求 5.1, 需求 5.2
   */
  async executeAnalysis(request: ExecuteAnalysisRequest): Promise<AnalysisResult> {
    try {
      // 1. Get and validate existing task
      const task = await this.getTaskById(request.taskId);
      if (!task) {
        throw new AnalysisError('任务不存在', 'TASK_NOT_FOUND', 404);
      }

      if (task.status !== 'pending') {
        throw new AnalysisError('任务状态不允许执行分析', 'INVALID_TASK_STATUS', 400);
      }

      // 2. Create complete workflow record
      const workflowRequest: CreateCompleteWorkflowRequest = {
        ticker: task.ticker,
        title: task.title,
        description: task.description,
        config: task.config || request.config || {},
        created_by: task.created_by,
      };

      const workflowId = await EnhancedLangGraphDatabase.createCompleteWorkflow(workflowRequest);

      // 3. Update task status and associate with workflow
      await this.updateTaskStatus(request.taskId, 'running', workflowId);

      // 4. Track active analysis
      this.activeAnalyses.set(workflowId, {
        workflowId,
        taskId: request.taskId,
      });

      // 5. Setup analysis monitoring
      this.setupAnalysisMonitoring(workflowId);

      // 6. Start LangGraph analysis asynchronously
      this.startLangGraphAnalysis(workflowId, task.ticker, workflowRequest.config)
        .then((result) => this.handleAnalysisSuccess(workflowId, result))
        .catch((error) => this.handleAnalysisError(workflowId, error));

      return {
        workflowId,
        taskId: request.taskId,
        status: 'started',
        message: '分析已启动',
      };
    } catch (error: any) {
      console.error('执行分析失败:', error);

      // Restore task status if analysis startup failed
      if (request.taskId) {
        try {
          await this.updateTaskStatus(request.taskId, 'pending');
        } catch (restoreError) {
          console.error('恢复任务状态失败:', restoreError);
        }
      }

      if (error instanceof AnalysisError) {
        throw error;
      }
      throw new AnalysisError(`执行分析失败: ${error.message}`);
    }
  }

  /**
   * Get analysis status with complete information
   * Requirements: 需求 2.1, 需求 2.2, 需求 6.1, 需求 6.2
   */
  async getAnalysisStatus(workflowId: string): Promise<AnalysisStatus> {
    try {
      // Get complete workflow status from database
      const completeStatus = await EnhancedLangGraphDatabase.getCompleteWorkflowStatus(workflowId);

      // Get associated task information
      const activeAnalysis = this.activeAnalyses.get(workflowId);
      const taskId = activeAnalysis?.taskId || '';

      return this.transformToAnalysisStatus(completeStatus as any, taskId);
    } catch (error: any) {
      console.error('获取分析状态失败:', error);
      throw new AnalysisError(`获取状态失败: ${error.message}`);
    }
  }

  /**
   * Stop analysis execution
   * Requirements: 需求 5.3, 需求 5.4
   */
  async stopAnalysis(workflowId: string): Promise<void> {
    try {
      const activeAnalysis = this.activeAnalyses.get(workflowId);
      if (!activeAnalysis) {
        throw new AnalysisError('分析不存在或未运行', 'ANALYSIS_NOT_FOUND', 404);
      }

      // Update workflow status to cancelled
      await EnhancedLangGraphDatabase.updateWorkflowStatus({
        workflow_id: workflowId,
        current_stage: 'cancelled',
        progress: 0,
        status: 'cancelled',
        error_message: '用户取消分析',
      });

      // Update task status
      await this.updateTaskStatus(activeAnalysis.taskId, 'pending');

      // Clean up active analysis
      this.activeAnalyses.delete(workflowId);

      // Log cancellation event
      await EnhancedLangGraphDatabase.logWorkflowEvent({
        workflow_id: workflowId,
        stage_name: 'cancellation',
        event_type: 'log',
        content: '用户取消分析',
        metadata: { cancelled_by: 'user' },
      });

      this.emit('analysisStopped', { workflowId, taskId: activeAnalysis.taskId });
    } catch (error: any) {
      console.error('停止分析失败:', error);
      throw new AnalysisError(`停止分析失败: ${error.message}`);
    }
  }

  // ============================================================================
  // Private Helper Methods
  // ============================================================================

  /**
   * Setup event handlers for the service
   */
  private setupEventHandlers(): void {
    // Remove any existing listeners first to prevent memory leaks
    if (this.sessionUpdateHandler) {
      langGraphService.removeListener('sessionUpdate', this.sessionUpdateHandler);
    }

    // Listen to LangGraph service events
    this.sessionUpdateHandler = (data: any) => {
      this.handleLangGraphUpdate(data);
    };

    langGraphService.on('sessionUpdate', this.sessionUpdateHandler);

    // 进程清理由全局处理器管理，这里不需要重复注册
  }

  /**
   * Get task by ID from task flow database
   */
  private async getTaskById(taskId: string): Promise<Task | null> {
    try {
      const task = await taskFlowDb.getTask(taskId);
      return task as Task | null;
    } catch (error) {
      console.error('获取任务失败:', error);
      return null;
    }
  }

  /**
   * Update task status and optionally associate with workflow
   */
  private async updateTaskStatus(
    taskId: string,
    status: string,
    workflowId?: string
  ): Promise<void> {
    try {
      await taskFlowDb.updateTaskStatus(taskId, status as any);

      // If workflow ID is provided, we could store it in task metadata
      // This would require extending the task flow database schema
      if (workflowId) {
        // For now, we'll log this association
        await taskFlowDb.logSystemEvent(
          'INFO',
          'AnalysisService',
          'updateTaskStatus',
          `任务 ${taskId} 关联到工作流 ${workflowId}`,
          { taskId, workflowId, status }
        );
      }
    } catch (error) {
      console.error('更新任务状态失败:', error);
      throw error;
    }
  }

  /**
   * Setup monitoring for analysis workflow
   * Requirements: 需求 1.6, 需求 2.1, 需求 2.2, 需求 2.3
   */
  private setupAnalysisMonitoring(workflowId: string): void {
    // Skip monitoring in test environment to avoid hanging tests
    if (process.env.NODE_ENV === 'test') {
      return;
    }

    // Set up periodic status checks
    const monitoringInterval = setInterval(async () => {
      try {
        const status = await this.getAnalysisStatus(workflowId);

        // Emit status update event
        this.emit('statusUpdate', {
          workflowId,
          status: status.status,
          progress: status.progress,
          currentStage: status.currentStage,
        });

        // Clean up monitoring if analysis is complete
        if (['completed', 'failed', 'cancelled'].includes(status.status)) {
          clearInterval(monitoringInterval);
          this.activeAnalyses.delete(workflowId);
        }
      } catch (error) {
        console.error('监控分析状态失败:', error);
        clearInterval(monitoringInterval);
        // Remove from active analyses on error
        this.activeAnalyses.delete(workflowId);
      }
    }, 5000); // Check every 5 seconds

    // Store interval for cleanup
    const activeAnalysis = this.activeAnalyses.get(workflowId);
    if (activeAnalysis) {
      (activeAnalysis as any).monitoringInterval = monitoringInterval;
    }
  }

  /**
   * Start LangGraph analysis with retry mechanism
   * Requirements: 需求 5.1, 需求 5.2, 需求 5.3
   */
  private async startLangGraphAnalysis(
    workflowId: string,
    ticker: string,
    config: any
  ): Promise<any> {
    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Update workflow status to running
        await EnhancedLangGraphDatabase.updateWorkflowStatus({
          workflow_id: workflowId,
          current_stage: attempt === 1 ? 'starting' : `retrying_${attempt}`,
          progress: 5,
          status: 'running',
          error_message: '',
        });

        // Log retry attempt if not first attempt
        if (attempt > 1) {
          await EnhancedLangGraphDatabase.logWorkflowEvent({
            workflow_id: workflowId,
            stage_name: 'retry',
            event_type: 'log',
            content: `重试分析 (第 ${attempt} 次尝试)`,
            metadata: {
              attempt,
              maxRetries,
              previousError: lastError?.message,
            },
          });
        }

        // Start LangGraph analysis
        const result = await langGraphService.analyzeStock(workflowId, ticker, config);

        // Log successful completion if there were previous failures
        if (attempt > 1) {
          await EnhancedLangGraphDatabase.logWorkflowEvent({
            workflow_id: workflowId,
            stage_name: 'recovery',
            event_type: 'log',
            content: `分析在第 ${attempt} 次尝试后成功完成`,
            metadata: { attempt, maxRetries },
          });
        }

        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.error(`LangGraph 分析失败 (尝试 ${attempt}/${maxRetries}):`, lastError);

        // Log the error
        await EnhancedLangGraphDatabase.logWorkflowEvent({
          workflow_id: workflowId,
          stage_name: 'error',
          event_type: 'error',
          content: `分析失败 (尝试 ${attempt}/${maxRetries}): ${lastError.message}`,
          metadata: {
            attempt,
            maxRetries,
            error: lastError.stack,
            willRetry: attempt < maxRetries,
          },
        });

        // If this is the last attempt, throw the error
        if (attempt === maxRetries) {
          throw new AnalysisError(
            `分析在 ${maxRetries} 次尝试后仍然失败: ${lastError.message}`,
            'ANALYSIS_MAX_RETRIES_EXCEEDED',
            500
          );
        }

        // Wait before retrying (exponential backoff)
        const waitTime = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s...
        await new Promise((resolve) => setTimeout(resolve, waitTime));
      }
    }

    // This should never be reached, but TypeScript requires it
    throw lastError || new Error('未知错误');
  }

  /**
   * Handle LangGraph service updates
   * Requirements: 需求 1.6, 需求 2.1, 需求 2.2
   */
  private handleLangGraphUpdate(data: any): void {
    const { workflowId, session } = data;

    if (this.activeAnalyses.has(workflowId)) {
      // Update workflow status in database
      this.updateWorkflowFromSession(workflowId, session).catch((error) => {
        console.error('更新工作流状态失败:', error);
      });

      // Emit real-time update
      this.emit('langGraphUpdate', {
        workflowId,
        currentStep: session.currentStep,
        isProcessing: session.isProcessing,
        error: session.error,
        progress: this.calculateProgressFromStep(session.currentStep),
      });
    }
  }

  /**
   * Update workflow status based on LangGraph session data
   * Requirements: 需求 1.6, 需求 2.1
   */
  private async updateWorkflowFromSession(workflowId: string, session: any): Promise<void> {
    try {
      const progress = this.calculateProgressFromStep(session.currentStep);
      const status = session.error ? 'failed' : session.isProcessing ? 'running' : 'completed';

      await EnhancedLangGraphDatabase.updateWorkflowStatus({
        workflow_id: workflowId,
        current_stage: session.currentStep,
        progress,
        status: status as any,
        error_message: session.error || undefined,
      });

      // Log the update event
      await EnhancedLangGraphDatabase.logWorkflowEvent({
        workflow_id: workflowId,
        stage_name: session.currentStep,
        event_type: 'state_change',
        content: `工作流进度更新: ${session.currentStep} (${progress}%)`,
        metadata: {
          progress,
          isProcessing: session.isProcessing,
          error: session.error,
        },
      });
    } catch (error) {
      console.error('更新工作流状态失败:', error);
    }
  }

  /**
   * Calculate progress percentage based on current step
   * Requirements: 需求 2.1, 需求 2.5
   */
  private calculateProgressFromStep(currentStep: string): number {
    const stepProgressMap: Record<string, number> = {
      created: 0,
      starting: 5,
      '开始分析...': 10,
      '初始化分析...': 10,
      '收集数据...': 20,
      data_collection: 20,
      '基本面分析...': 40,
      fundamental_analysis: 40,
      '技术分析...': 60,
      technical_analysis: 60,
      '情绪分析...': 70,
      sentiment_analysis: 70,
      '新闻分析...': 75,
      news_analysis: 75,
      '研究辩论...': 85,
      research_debate: 85,
      '风险评估...': 90,
      risk_assessment: 90,
      '生成决策...': 95,
      final_decision: 95,
      分析完成: 100,
      completed: 100,
      finished: 100,
      failed: 0,
      cancelled: 0,
      error: 0,
    };

    return stepProgressMap[currentStep] || 50; // Default to 50% if step not recognized
  }

  /**
   * Handle successful analysis completion
   * Requirements: 需求 1.3, 需求 2.4, 需求 9.1
   */
  private async handleAnalysisSuccess(workflowId: string, result: any): Promise<void> {
    try {
      const activeAnalysis = this.activeAnalyses.get(workflowId);
      if (!activeAnalysis) return;

      // Update workflow status to completed
      await EnhancedLangGraphDatabase.updateWorkflowStatus({
        workflow_id: workflowId,
        current_stage: 'completed',
        progress: 100,
        status: 'completed',
        error_message: '',
      });

      // Update task status to completed
      await this.updateTaskStatus(activeAnalysis.taskId, 'completed');

      // Log completion event
      await EnhancedLangGraphDatabase.logWorkflowEvent({
        workflow_id: workflowId,
        stage_name: 'completion',
        event_type: 'log',
        content: '分析成功完成',
        metadata: { result: result },
      });

      // Emit completion event
      this.emit('analysisCompleted', {
        workflowId,
        taskId: activeAnalysis.taskId,
        result,
      });

      // Clean up
      this.activeAnalyses.delete(workflowId);
    } catch (error) {
      console.error('处理分析成功失败:', error);
      await this.handleAnalysisError(workflowId, error);
    }
  }

  /**
   * Handle analysis errors
   * Requirements: 需求 5.1, 需求 5.2, 需求 9.1
   */
  private async handleAnalysisError(workflowId: string, error: any): Promise<void> {
    try {
      const activeAnalysis = this.activeAnalyses.get(workflowId);
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Update workflow status to failed
      await EnhancedLangGraphDatabase.updateWorkflowStatus({
        workflow_id: workflowId,
        current_stage: 'failed',
        progress: 0,
        status: 'failed',
        error_message: errorMessage,
      });

      // Update task status to failed if we have the task ID
      if (activeAnalysis) {
        await this.updateTaskStatus(activeAnalysis.taskId, 'failed');
      }

      // Log error event
      await EnhancedLangGraphDatabase.logWorkflowEvent({
        workflow_id: workflowId,
        stage_name: 'error',
        event_type: 'error',
        content: `分析失败: ${errorMessage}`,
        metadata: {
          error: error.stack || errorMessage,
          timestamp: new Date().toISOString(),
        },
      });

      // Emit error event
      this.emit('analysisError', {
        workflowId,
        taskId: activeAnalysis?.taskId,
        error: errorMessage,
      });

      // Clean up
      if (activeAnalysis) {
        this.activeAnalyses.delete(workflowId);
      }
    } catch (dbError) {
      console.error('处理分析错误失败:', dbError);
    }
  }

  /**
   * Transform complete workflow status to analysis status format
   */
  private transformToAnalysisStatus(
    completeStatus: CompleteWorkflowStatus,
    taskId: string
  ): AnalysisStatus {
    return {
      workflowId: completeStatus.workflow.workflow_id,
      taskId,
      status: completeStatus.workflow.status,
      progress: completeStatus.workflow.progress,
      currentStage: completeStatus.workflow.current_stage,
      ticker: completeStatus.workflow.ticker,
      title: completeStatus.workflow.title,
      createdAt: completeStatus.workflow.created_at.toString(),
      startedAt: completeStatus.workflow.started_at?.toString(),
      completedAt: completeStatus.workflow.completed_at?.toString(),
      analystReports: completeStatus.analystReports,
      researchReports: completeStatus.researchReports,
      finalDecision: completeStatus.finalDecision,
      recentEvents: completeStatus.recentEvents,
      error: completeStatus.workflow.error_message || undefined,
    };
  }

  /**
   * Get all active analyses
   */
  getActiveAnalyses(): Array<{ workflowId: string; taskId: string }> {
    return Array.from(this.activeAnalyses.values());
  }

  /**
   * Recover interrupted analyses on service startup
   * Requirements: 需求 5.4, 需求 5.5
   */
  private async recoverInterruptedAnalyses(): Promise<void> {
    try {
      console.log('检查中断的分析任务...');

      // Find workflows that are in running state but may have been interrupted
      const runningWorkflows = await this.findRunningWorkflows();

      for (const workflow of runningWorkflows) {
        console.log(`发现中断的分析: ${workflow.workflow_id}`);

        // Check if the workflow is actually still running
        const isStillRunning = await this.checkIfWorkflowStillRunning(workflow.workflow_id);

        if (!isStillRunning) {
          // Mark as failed and log recovery attempt
          await EnhancedLangGraphDatabase.updateWorkflowStatus({
            workflow_id: workflow.workflow_id,
            current_stage: 'recovery_failed',
            progress: 0,
            status: 'failed',
            error_message: '服务重启导致分析中断',
          });

          await EnhancedLangGraphDatabase.logWorkflowEvent({
            workflow_id: workflow.workflow_id,
            stage_name: 'recovery',
            event_type: 'error',
            content: '检测到中断的分析，标记为失败',
            metadata: {
              reason: 'service_restart',
              recovery_time: new Date().toISOString(),
            },
          });

          // Update associated task status if possible
          await this.updateTaskStatusForWorkflow(workflow.workflow_id, 'failed');
        }
      }

      console.log(`恢复检查完成，处理了 ${runningWorkflows.length} 个工作流`);
    } catch (error) {
      console.error('恢复中断分析失败:', error);
    }
  }

  /**
   * Find workflows that are in running state
   */
  private async findRunningWorkflows(): Promise<any[]> {
    // This would need to be implemented in the database layer
    // For now, return empty array as we don't have this query method
    return [];
  }

  /**
   * Check if a workflow is actually still running
   */
  private async checkIfWorkflowStillRunning(workflowId: string): Promise<boolean> {
    // Check if the workflow has a LangGraph session that's still active
    const session = langGraphService.getSession(workflowId);
    return session?.isProcessing || false;
  }

  /**
   * Update task status for a given workflow
   */
  private async updateTaskStatusForWorkflow(workflowId: string, status: string): Promise<void> {
    try {
      // This would require a reverse lookup from workflow to task
      // For now, we'll just log the attempt
      await taskFlowDb.logSystemEvent(
        'INFO',
        'AnalysisService',
        'updateTaskStatusForWorkflow',
        `尝试更新工作流 ${workflowId} 关联任务状态为 ${status}`,
        { workflowId, status }
      );
    } catch (error) {
      console.error('更新任务状态失败:', error);
    }
  }

  /**
   * Cleanup resources
   */
  private async cleanup(): Promise<void> {
    console.log('清理 AnalysisService 资源...');

    // Clear all monitoring intervals
    for (const [workflowId, analysis] of Array.from(this.activeAnalyses.entries())) {
      const interval = (analysis as any).monitoringInterval;
      if (interval) {
        clearInterval(interval);
      }
    }

    // Clear active analyses
    this.activeAnalyses.clear();

    // Remove event listeners from langGraphService
    if (this.sessionUpdateHandler) {
      langGraphService.removeListener('sessionUpdate', this.sessionUpdateHandler);
      this.sessionUpdateHandler = undefined;
    }

    // Remove all listeners from this instance
    this.removeAllListeners();
  }

  /**
   * Public cleanup method for external use
   */
  public destroy(): void {
    this.cleanup();
  }
}

// ============================================================================
// Export Service Instance (Singleton)
// ============================================================================

let analysisServiceInstance: AnalysisService | null = null;

export const getAnalysisService = (): AnalysisService => {
  if (!analysisServiceInstance) {
    analysisServiceInstance = new AnalysisService();
  }
  return analysisServiceInstance;
};

export const analysisService = getAnalysisService();

// 全局清理函数
export const cleanupAnalysisService = (): void => {
  if (analysisServiceInstance) {
    analysisServiceInstance.destroy();
    analysisServiceInstance = null;
  }
};

export default AnalysisService;
