import { NextRequest, NextResponse } from 'next/server';
import { validateEmailFormat, generateVerificationCode, sendVerificationEmail, checkEmailRateLimit } from '@/lib/email';
import { createVerificationCode, checkVerificationCodeRateLimit, getVerificationCodeStats } from '@/lib/verification-code-db';
import { getUserByEmail } from '@/lib/user-db';
import { error, success } from '@/lib/api-helpers';

export async function POST(request: NextRequest) {
  try {
    const { email, type = 'register' } = await request.json();

    // 验证输入
    if (!email) {
      return error('请输入邮箱地址', 400);
    }

    if (!validateEmailFormat(email)) {
      return error('请输入有效的邮箱地址', 400);
    }

    // 验证验证码类型
    const validTypes = ['register', 'reset_password', 'change_email'];
    if (!validTypes.includes(type)) {
      return error('无效的验证码类型', 400);
    }

    // 获取客户端信息
    const ipAddress = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // 根据类型进行不同的验证
    if (type === 'register') {
      // 注册验证码：检查邮箱是否已被注册
      const existingUser = await getUserByEmail(email);
      if (existingUser) {
        return error('该邮箱已被注册', 409);
      }
    } else if (type === 'reset_password') {
      // 密码重置验证码：检查邮箱是否存在
      const existingUser = await getUserByEmail(email);
      if (!existingUser) {
        return error('该邮箱未注册', 404);
      }
    }

    // 检查验证码发送频率限制（1分钟内只能发送一次）
    const rateLimit = await checkVerificationCodeRateLimit(email, type, 1);
    if (!rateLimit.allowed) {
      return error(`请等待 ${rateLimit.waitTime} 分钟后再试`, 429);
    }

    // 检查邮件发送频率限制
    const emailRateLimit = await checkEmailRateLimit(email, 'verification_code', 1);
    if (!emailRateLimit.allowed) {
      return error(`请等待 ${emailRateLimit.waitTime} 分钟后再试`, 429);
    }

    // 获取24小时内的统计信息，防止滥用
    const stats = await getVerificationCodeStats(email, type, 24);
    if (stats.totalSent >= 10) {
      return error('24小时内发送次数过多，请稍后再试', 429);
    }

    // 生成6位数字验证码
    const code = generateVerificationCode();
    const expiresInMinutes = 10; // 10分钟有效期

    // 保存验证码到数据库
    const codeId = await createVerificationCode(
      email,
      code,
      type,
      expiresInMinutes,
      ipAddress,
      userAgent
    );

    // 发送验证码邮件
    const emailResult = await sendVerificationEmail(
      {
        email,
        code,
        type,
        expiresInMinutes,
      },
      ipAddress
    );

    if (!emailResult.success) {
      return error(`邮件发送失败: ${emailResult.error}`, 500);
    }

    // 返回成功响应（不包含验证码）
    return success(
      {
        codeId,
        email,
        expiresInMinutes,
        messageId: emailResult.messageId,
      },
      '验证码已发送到您的邮箱'
    );
  } catch (err: any) {
    console.error('发送验证码失败:', err);
    return error('服务器内部错误', 500);
  }
}