// LangGraph 工作流状态管理 Hook
import {
  CreateWorkflowRequest,
  langGraphApi,
  QueryWorkflowsOptions,
  UpdateWorkflowStageRequest,
  WorkflowDetails,
  WorkflowStatus,
} from '@/lib/api';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useState } from 'react';

// 包装 API 调用以处理错误
const workflowApiWrapper = {
  // 创建工作流
  createWorkflow: async (data: CreateWorkflowRequest) => {
    const response = await langGraphApi.createWorkflow(data);
    if (!response.success) {
      throw new Error(response.message || '创建工作流失败');
    }
    return response;
  },

  // 更新工作流状态
  updateWorkflowStage: async (data: UpdateWorkflowStageRequest) => {
    const response = await langGraphApi.updateWorkflowStage(data);
    if (!response.success) {
      throw new Error(response.message || '更新工作流状态失败');
    }
    return response;
  },

  // 获取工作流状态
  getWorkflowStatus: async (workflowId?: string): Promise<WorkflowDetails> => {
    const response = await langGraphApi.getWorkflowStatus(workflowId);
    if (!response.success) {
      throw new Error(response.message || '获取工作流状态失败');
    }
    return response.data;
  },

  // 查询工作流列表
  queryWorkflows: async (options: QueryWorkflowsOptions = {}): Promise<any[]> => {
    const response = await langGraphApi.queryWorkflows(options);
    if (!response.success) {
      throw new Error(response.message || '查询工作流列表失败');
    }
    return response.data;
  },
};

// 主要的 Hook
export function useLangGraphWorkflow(workflowId?: string) {
  const queryClient = useQueryClient();
  const [isPolling, setIsPolling] = useState(false);

  // 查询工作流详细状态
  const {
    data: workflowDetails,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['langgraph-workflow', workflowId],
    queryFn: () => workflowApiWrapper.getWorkflowStatus(workflowId),
    enabled: !!workflowId,
    refetchInterval: isPolling ? 2000 : false, // 轮询间隔
    refetchIntervalInBackground: false,
  });

  // 创建工作流
  const createWorkflowMutation = useMutation({
    mutationFn: workflowApiWrapper.createWorkflow,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['langgraph-workflow'] });
    },
  });

  // 更新工作流状态
  const updateWorkflowMutation = useMutation({
    mutationFn: workflowApiWrapper.updateWorkflowStage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['langgraph-workflow'] });
    },
  });

  // 开始轮询
  const startPolling = useCallback(() => {
    setIsPolling(true);
  }, []);

  // 停止轮询
  const stopPolling = useCallback(() => {
    setIsPolling(false);
  }, []);

  // 手动刷新
  const refresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // 创建工作流
  const createWorkflow = useCallback(
    (data: CreateWorkflowRequest) => {
      return createWorkflowMutation.mutateAsync(data);
    },
    [createWorkflowMutation]
  );

  // 更新工作流状态
  const updateWorkflowStage = useCallback(
    (data: UpdateWorkflowStageRequest) => {
      return updateWorkflowMutation.mutateAsync(data);
    },
    [updateWorkflowMutation]
  );

  // 计算完成进度
  const completionProgress = workflowDetails?.status
    ? {
        total: 9, // 总共9个阶段
        completed: [
          workflowDetails.status.fundamental_completed,
          workflowDetails.status.technical_completed,
          workflowDetails.status.sentiment_completed,
          workflowDetails.status.news_completed,
          workflowDetails.status.bull_completed,
          workflowDetails.status.bear_completed,
          workflowDetails.status.consensus_completed,
          workflowDetails.status.risk_assessment_completed,
          workflowDetails.status.decision_completed,
        ].filter(Boolean).length,
        percentage: Math.round(
          ([
            workflowDetails.status.fundamental_completed,
            workflowDetails.status.technical_completed,
            workflowDetails.status.sentiment_completed,
            workflowDetails.status.news_completed,
            workflowDetails.status.bull_completed,
            workflowDetails.status.bear_completed,
            workflowDetails.status.consensus_completed,
            workflowDetails.status.risk_assessment_completed,
            workflowDetails.status.decision_completed,
          ].filter(Boolean).length /
            9) *
            100
        ),
      }
    : null;

  return {
    // 数据
    workflowDetails,
    status: workflowDetails?.status,
    analysts: workflowDetails?.analysts,
    researchers: workflowDetails?.researchers,
    debate: workflowDetails?.debate,
    consensus: workflowDetails?.consensus,
    risk: workflowDetails?.risk,
    decision: workflowDetails?.decision,
    messages: workflowDetails?.messages || [],
    statistics: workflowDetails?.statistics,

    // 状态
    isLoading,
    error,
    isPolling,
    completionProgress,

    // 操作
    createWorkflow,
    updateWorkflowStage,
    startPolling,
    stopPolling,
    refresh,

    // 变异状态
    isCreating: createWorkflowMutation.isPending,
    isUpdating: updateWorkflowMutation.isPending,
    createError: createWorkflowMutation.error,
    updateError: updateWorkflowMutation.error,
  };
}

// 工作流列表 Hook
export function useLangGraphWorkflowList(options: QueryWorkflowsOptions = {}) {
  const {
    data: workflows,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['langgraph-workflows', options],
    queryFn: () => workflowApiWrapper.queryWorkflows(options),
  });

  return {
    workflows: workflows || [],
    isLoading,
    error,
    refresh: refetch,
  };
}

// 实时状态监控 Hook
export function useLangGraphWorkflowMonitor(workflowId?: string) {
  const { workflowDetails, startPolling, stopPolling, isPolling } =
    useLangGraphWorkflow(workflowId);

  // 自动开始/停止轮询
  useState(() => {
    if (workflowDetails?.status?.workflow_status === 'running') {
      startPolling();
    } else {
      stopPolling();
    }
  });

  return {
    workflowDetails,
    isMonitoring: isPolling,
    startMonitoring: startPolling,
    stopMonitoring: stopPolling,
  };
}

// 重新导出类型
export type {
  CreateWorkflowRequest,
  QueryWorkflowsOptions,
  UpdateWorkflowStageRequest,
  WorkflowDetails,
  WorkflowStatus,
};
