import { Metadata } from 'next';
import {
  generateEnglishPageTitle,
  generateEnglishPageDescription,
  generateEnglishKeywords,
} from '@/utils/seo-en';

// English homepage SEO metadata
export function generateEnglishHomeMetadata(): Metadata {
  const title = generateEnglishPageTitle();
  const description = generateEnglishPageDescription();
  const keywords = generateEnglishKeywords();

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      locale: 'en_US',
      url: 'https://tradingagent.top/en',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    alternates: {
      canonical: 'https://tradingagent.top/en',
      languages: {
        'zh-CN': 'https://tradingagent.top',
        'en-US': 'https://tradingagent.top/en',
      },
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

// English create task page SEO metadata
export function generateEnglishCreateTaskMetadata(): Metadata {
  const title = generateEnglishPageTitle('Create Stock Analysis Task');
  const description = generateEnglishPageDescription(
    'Create professional AI stock analysis tasks, select target stocks, configure analyst teams, and get comprehensive investment decision support.'
  );
  const keywords = generateEnglishKeywords([
    'create task',
    'stock analysis task',
    'investment analysis',
    'stock selection',
    'analysis configuration',
  ]);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      locale: 'en_US',
      url: 'https://tradingagent.top/en/create-task',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    alternates: {
      canonical: 'https://tradingagent.top/en/create-task',
      languages: {
        'zh-CN': 'https://tradingagent.top/create-task',
        'en-US': 'https://tradingagent.top/en/create-task',
      },
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

// English tasks list page SEO metadata
export function generateEnglishTasksMetadata(): Metadata {
  const title = generateEnglishPageTitle('Stock Analysis Tasks');
  const description = generateEnglishPageDescription(
    'View all stock analysis task status and results, track AI analysis progress, get professional investment advice and market insights.'
  );
  const keywords = generateEnglishKeywords([
    'task list',
    'analysis results',
    'investment advice',
    'market analysis',
    'stock reports',
  ]);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      locale: 'en_US',
      url: 'https://tradingagent.top/en/tasks',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    alternates: {
      canonical: 'https://tradingagent.top/en/tasks',
      languages: {
        'zh-CN': 'https://tradingagent.top/tasks',
        'en-US': 'https://tradingagent.top/en/tasks',
      },
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

// English task detail page SEO metadata
export function generateEnglishTaskDetailMetadata(taskId: string, stockSymbol?: string): Metadata {
  const stockName = stockSymbol || 'Stock';
  const title = generateEnglishPageTitle(`${stockName} Analysis Report - Task ${taskId}`);
  const description = generateEnglishPageDescription(
    `View detailed AI analysis report for ${stockName}, including technical analysis, fundamental analysis, news analysis, sentiment analysis and more.`
  );
  const keywords = generateEnglishKeywords([
    `${stockName} analysis`,
    'stock report',
    'investment analysis',
    'technical analysis',
    'fundamental analysis',
    'news analysis',
    'sentiment analysis',
    'investment recommendation',
    `${stockSymbol}`,
  ]);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'article',
      locale: 'en_US',
      url: `https://tradingagent.top/en/tasks/${taskId}`,
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    alternates: {
      canonical: `https://tradingagent.top/en/tasks/${taskId}`,
      languages: {
        'zh-CN': `https://tradingagent.top/tasks/${taskId}`,
        'en-US': `https://tradingagent.top/en/tasks/${taskId}`,
      },
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

// English login page SEO metadata
export function generateEnglishLoginMetadata(): Metadata {
  const title = generateEnglishPageTitle('User Login');
  const description = generateEnglishPageDescription(
    'Login to TradingAgents platform, access your stock analysis tasks and portfolios, enjoy professional AI investment decision services.'
  );
  const keywords = generateEnglishKeywords([
    'user login',
    'account login',
    'platform login',
    'sign in',
  ]);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      locale: 'en_US',
      url: 'https://tradingagent.top/en/auth/login',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    alternates: {
      canonical: 'https://tradingagent.top/en/auth/login',
      languages: {
        'zh-CN': 'https://tradingagent.top/auth/login',
        'en-US': 'https://tradingagent.top/en/auth/login',
      },
    },
    robots: {
      index: false, // Login pages usually don't need indexing
      follow: true,
    },
  };
}

// English register page SEO metadata
export function generateEnglishRegisterMetadata(): Metadata {
  const title = generateEnglishPageTitle('User Registration');
  const description = generateEnglishPageDescription(
    'Register for TradingAgents account, start using professional AI stock analysis services, get personalized investment decision support.'
  );
  const keywords = generateEnglishKeywords([
    'user registration',
    'account registration',
    'free registration',
    'sign up',
  ]);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      locale: 'en_US',
      url: 'https://tradingagent.top/en/auth/register',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    alternates: {
      canonical: 'https://tradingagent.top/en/auth/register',
      languages: {
        'zh-CN': 'https://tradingagent.top/auth/register',
        'en-US': 'https://tradingagent.top/en/auth/register',
      },
    },
    robots: {
      index: false, // Register pages usually don't need indexing
      follow: true,
    },
  };
}

// English about page SEO metadata
export function generateEnglishAboutMetadata(): Metadata {
  const title = generateEnglishPageTitle('About TradingAgents');
  const description = generateEnglishPageDescription(
    'Learn about TradingAgents - the innovative AI-powered stock analysis platform that revolutionizes investment decision making through multi-agent technology.'
  );
  const keywords = generateEnglishKeywords([
    'about us',
    'company information',
    'AI technology',
    'investment platform',
    'financial innovation',
  ]);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      locale: 'en_US',
      url: 'https://tradingagent.top/en/about',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    alternates: {
      canonical: 'https://tradingagent.top/en/about',
      languages: {
        'zh-CN': 'https://tradingagent.top/about',
        'en-US': 'https://tradingagent.top/en/about',
      },
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}
