import { ImageResponse } from 'next/og';
import { NextRequest } from 'next/server';

export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const title = searchParams.get('title') || 'TradingAgents';
    const description = searchParams.get('description') || 'AI智能股票分析平台';

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#fff',
            backgroundImage: 'linear-gradient(45deg, #2563eb 0%, #7c3aed 100%)',
            position: 'relative',
          }}
        >
          {/* 背景装饰 */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background:
                'radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 70% 70%, rgba(255,255,255,0.1) 0%, transparent 50%)',
            }}
          />

          {/* 主要内容 */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              textAlign: 'center',
              padding: '80px',
              position: 'relative',
              zIndex: 1,
            }}
          >
            {/* 标题 */}
            <h1
              style={{
                fontSize: '72px',
                fontWeight: 'bold',
                color: 'white',
                margin: '0 0 32px 0',
                textShadow: '0 4px 8px rgba(0,0,0,0.2)',
                lineHeight: 1.1,
              }}
            >
              {title}
            </h1>

            {/* 描述 */}
            <p
              style={{
                fontSize: '32px',
                color: 'rgba(255,255,255,0.9)',
                margin: '0',
                maxWidth: '800px',
                lineHeight: 1.4,
                textShadow: '0 2px 4px rgba(0,0,0,0.1)',
              }}
            >
              {description}
            </p>

            {/* 装饰元素 */}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: '48px',
                gap: '24px',
              }}
            >
              <div
                style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: 'rgba(255,255,255,0.8)',
                }}
              />
              <div
                style={{
                  width: '120px',
                  height: '2px',
                  backgroundColor: 'rgba(255,255,255,0.6)',
                }}
              />
              <div
                style={{
                  fontSize: '24px',
                  color: 'rgba(255,255,255,0.9)',
                  fontWeight: 'bold',
                }}
              >
                AI 驱动的投资决策
              </div>
              <div
                style={{
                  width: '120px',
                  height: '2px',
                  backgroundColor: 'rgba(255,255,255,0.6)',
                }}
              />
              <div
                style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: 'rgba(255,255,255,0.8)',
                }}
              />
            </div>
          </div>

          {/* 品牌标识 */}
          <div
            style={{
              position: 'absolute',
              bottom: '40px',
              right: '40px',
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
            }}
          >
            <div
              style={{
                fontSize: '20px',
                color: 'rgba(255,255,255,0.8)',
                fontWeight: '500',
              }}
            >
              tradingagents.com
            </div>
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
      }
    );
  } catch (e: any) {
    console.log(`${e.message}`);
    return new Response(`Failed to generate the image`, {
      status: 500,
    });
  }
}
