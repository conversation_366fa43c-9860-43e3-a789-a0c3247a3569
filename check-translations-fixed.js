const fs = require('fs');

// 读取页面文件
const pageContent = fs.readFileSync('src/app/[locale]/page.tsx', 'utf8');

// 读取翻译文件
const zhTranslations = JSON.parse(fs.readFileSync('src/messages/zh.json', 'utf8'));
const enTranslations = JSON.parse(fs.readFileSync('src/messages/en.json', 'utf8'));

// 提取页面中使用的翻译键
const translationKeyRegex = /t\('([^']+)'\)/g;
const usedKeys = [];
let match;

while ((match = translationKeyRegex.exec(pageContent)) !== null) {
    usedKeys.push(match[1]);
}

// 去重
const uniqueKeys = [...new Set(usedKeys)];

console.log('页面中使用的唯一翻译键数量:', uniqueKeys.length);

// 检查翻译键是否存在
function checkTranslationKey(key, translations) {
    const keys = key.split('.');
    let current = translations;

    // 页面使用的是homepage命名空间
    if (current.homepage) {
        current = current.homepage;
    }

    for (const k of keys) {
        if (current && typeof current === 'object' && k in current) {
            current = current[k];
        } else {
            return false;
        }
    }

    return typeof current === 'string';
}

console.log('\n检查翻译键是否存在:');
const missingKeys = {
    zh: [],
    en: []
};

for (const key of uniqueKeys) {
    if (!checkTranslationKey(key, zhTranslations)) {
        missingKeys.zh.push(key);
    }
    if (!checkTranslationKey(key, enTranslations)) {
        missingKeys.en.push(key);
    }
}

if (missingKeys.zh.length > 0) {
    console.log('\n中文翻译中缺失的键:');
    missingKeys.zh.forEach(key => console.log(`  - ${key}`));
} else {
    console.log('\n中文翻译完整 ✓');
}

if (missingKeys.en.length > 0) {
    console.log('\n英文翻译中缺失的键:');
    missingKeys.en.forEach(key => console.log(`  - ${key}`));
} else {
    console.log('\n英文翻译完整 ✓');
}

// 显示一些示例键的检查结果
console.log('\n示例键检查:');
const testKeys = ['hero.title', 'features.title', 'cases.title'];
for (const key of testKeys) {
    const zhExists = checkTranslationKey(key, zhTranslations);
    const enExists = checkTranslationKey(key, enTranslations);
    console.log(`${key}: ZH=${zhExists}, EN=${enExists}`);
}