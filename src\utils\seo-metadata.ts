import { Metadata } from 'next';
import { generatePageTitle, generatePageDescription, generateKeywords } from '@/utils/seo';

// 创建任务页面的 SEO 配置
export function generateCreateTaskMetadata(): Metadata {
  const title = generatePageTitle('创建股票分析任务');
  const description = generatePageDescription(
    '创建专业的AI股票分析任务，选择目标股票、配置分析师团队，获得全方位的投资决策支持。'
  );
  const keywords = generateKeywords([
    '创建任务',
    '股票分析任务',
    '投资分析',
    '股票选择',
    '分析配置',
  ]);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

// 任务列表页面的 SEO 配置
export function generateTasksMetadata(): Metadata {
  const title = generatePageTitle('股票分析任务列表');
  const description = generatePageDescription(
    '查看所有股票分析任务的状态和结果，跟踪AI分析进度，获取专业的投资建议和市场洞察。'
  );
  const keywords = generateKeywords(['任务列表', '分析结果', '投资建议', '市场分析', '股票报告']);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

// 任务详情页面的 SEO 配置
export function generateTaskDetailMetadata(taskId: string, stockSymbol?: string): Metadata {
  const stockName = stockSymbol || '股票';
  const title = generatePageTitle(`${stockName} 分析报告 - 任务 ${taskId}`);
  const description = generatePageDescription(
    `查看 ${stockName} 的详细AI分析报告，包含技术面、基本面、新闻面、情绪面等多维度专业分析。`
  );
  const keywords = generateKeywords([
    `${stockName}分析`,
    '股票报告',
    '投资分析',
    '技术分析',
    '基本面分析',
    '新闻分析',
    '情绪分析',
    '投资建议',
    `${stockSymbol}`,
  ]);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'article',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

// 登录页面的 SEO 配置
export function generateLoginMetadata(): Metadata {
  const title = generatePageTitle('用户登录');
  const description = generatePageDescription(
    '登录 TradingAgents 平台，访问您的股票分析任务和投资组合，享受专业的AI投资决策服务。'
  );
  const keywords = generateKeywords(['用户登录', '账户登录', '平台登录']);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    robots: {
      index: false, // 登录页面通常不需要被索引
      follow: true,
    },
  };
}

// 注册页面的 SEO 配置
export function generateRegisterMetadata(): Metadata {
  const title = generatePageTitle('用户注册');
  const description = generatePageDescription(
    '注册 TradingAgents 账户，开始使用专业的AI股票分析服务，获得个性化的投资决策支持。'
  );
  const keywords = generateKeywords(['用户注册', '账户注册', '免费注册', '平台注册']);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    robots: {
      index: false, // 注册页面通常不需要被索引
      follow: true,
    },
  };
}
