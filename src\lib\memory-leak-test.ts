// 内存泄漏测试脚本
// 用于验证 EventTarget 和 EventEmitter 监听器是否正确管理

import { EventEmitter } from 'events';
import { abortSignalManager, getAbortSignalStats } from './abort-signal-manager';
import { analysisService } from './analysis-service';
import { langGraphService } from './langgraph-server';

export function testEventListeners(): void {
  console.log('=== 内存泄漏测试开始 ===');

  // 1. 测试 AbortSignal 管理器
  console.log('\n1. AbortSignal 管理器测试:');
  const stats1 = getAbortSignalStats();
  console.log(`初始状态 - 监听器数量: ${stats1.listenerCount}, 已中止: ${stats1.isAborted}`);

  // 添加一些监听器
  const listener1 = () => console.log('监听器1');
  const listener2 = () => console.log('监听器2');
  
  abortSignalManager.addListener(listener1);
  abortSignalManager.addListener(listener2);
  
  const stats2 = getAbortSignalStats();
  console.log(`添加监听器后 - 监听器数量: ${stats2.listenerCount}`);

  // 移除监听器
  abortSignalManager.removeListener(listener1);
  const stats3 = getAbortSignalStats();
  console.log(`移除一个监听器后 - 监听器数量: ${stats3.listenerCount}`);

  // 2. 测试 EventEmitter 监听器数量
  console.log('\n2. EventEmitter 监听器测试:');
  console.log(`AnalysisService 监听器数量: ${analysisService.listenerCount('statusUpdate')}`);
  console.log(`LangGraphService 监听器数量: ${langGraphService.listenerCount('sessionUpdate')}`);

  // 3. 测试进程监听器
  console.log('\n3. 进程监听器测试:');
  console.log(`SIGINT 监听器数量: ${process.listenerCount('SIGINT')}`);
  console.log(`SIGTERM 监听器数量: ${process.listenerCount('SIGTERM')}`);
  console.log(`uncaughtException 监听器数量: ${process.listenerCount('uncaughtException')}`);
  console.log(`unhandledRejection 监听器数量: ${process.listenerCount('unhandledRejection')}`);

  console.log('\n=== 内存泄漏测试完成 ===');
}

export function monitorEventListeners(): void {
  console.log('=== 开始监控事件监听器 ===');
  
  const interval = setInterval(() => {
    const stats = getAbortSignalStats();
    console.log(`[${new Date().toISOString()}] AbortSignal 监听器: ${stats.listenerCount}`);
    console.log(`[${new Date().toISOString()}] AnalysisService 监听器: ${analysisService.listenerCount('statusUpdate')}`);
    console.log(`[${new Date().toISOString()}] LangGraphService 监听器: ${langGraphService.listenerCount('sessionUpdate')}`);
    console.log(`[${new Date().toISOString()}] 进程 SIGINT 监听器: ${process.listenerCount('SIGINT')}`);
    console.log('---');
  }, 5000);

  // 10秒后停止监控
  setTimeout(() => {
    clearInterval(interval);
    console.log('=== 监控结束 ===');
  }, 30000);
}

// 如果直接运行此文件
if (require.main === module) {
  testEventListeners();
  monitorEventListeners();
}