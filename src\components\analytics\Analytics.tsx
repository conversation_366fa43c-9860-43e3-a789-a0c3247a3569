import Script from 'next/script';

interface AnalyticsProps {
  googleAnalyticsId?: string;
  baiduAnalyticsId?: string;
}

export function Analytics({
  googleAnalyticsId = process.env.NEXT_PUBLIC_GA_ID,
  baiduAnalyticsId = process.env.NEXT_PUBLIC_BAIDU_ANALYTICS_ID,
}: AnalyticsProps) {
  return (
    <>
      {/* Google Analytics */}
      {googleAnalyticsId && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsId}`}
            strategy="afterInteractive"
          />
          
          <Script id="google-analytics" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${googleAnalyticsId}');
            `}
          </Script>
        </>
      )}

      {/* 百度统计 */}
      {baiduAnalyticsId && (
        <Script id="baidu-analytics" strategy="afterInteractive">
          {`
            var _hmt = _hmt || [];
            (function() {
              var hm = document.createElement("script");
              hm.src = "https://hm.baidu.com/hm.js?${baiduAnalyticsId}";
              var s = document.getElementsByTagName("script")[0]; 
              s.parentNode.insertBefore(hm, s);
            })();
          `}
        </Script>
      )}

      {/* Microsoft Clarity (可选) */}
      {process.env.NEXT_PUBLIC_CLARITY_ID && process.env.NEXT_PUBLIC_CLARITY_ID !== 'your-clarity-id' && (
        <Script id="microsoft-clarity" strategy="afterInteractive">
          {`
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "${process.env.NEXT_PUBLIC_CLARITY_ID}");
          `}
        </Script>
      )}
    </>
  );
}

// 事件跟踪函数
export const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, {
      ...parameters,
      timestamp: new Date().toISOString(),
    });
  }
};

// 页面浏览跟踪
export const trackPageView = (url: string, title?: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', process.env.NEXT_PUBLIC_GA_ID || '', {
      page_location: url,
      page_title: title || document.title,
    });
  }
};

// 转化事件跟踪
export const trackConversion = (action: string, value?: number) => {
  trackEvent('conversion', {
    action,
    value,
    currency: 'CNY',
  });
};

// 用户交互跟踪
export const trackUserInteraction = (element: string, action: string) => {
  trackEvent('user_interaction', {
    element,
    action,
  });
};

// gtag 类型已在 src/types/global.d.ts 中声明
