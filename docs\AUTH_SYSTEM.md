# 认证系统文档

## 概述

本项目实现了一个完整的 JWT 认证系统，支持自动 token 刷新机制，确保用户在使用过程中不会因为 access token 过期而被强制登出。

## 核心特性

### 🔐 双 Token 机制
- **Access Token**: 短期有效（60分钟），用于日常 API 访问
- **Refresh Token**: 长期有效（7天），用于刷新 access token

### 🔄 自动刷新机制
- 服务端中间件自动检测过期的 access token
- 客户端 hook 提供自动重试功能
- 定时刷新机制（每50分钟自动刷新）

### 🛡️ 安全特性
- HttpOnly Cookies 防止 XSS 攻击
- 安全的密码哈希（bcrypt）
- JWT 签名验证
- 自动清理过期 token

## 文件结构

```
src/
├── lib/
│   └── auth.ts                 # 核心认证逻辑
├── middleware/
│   └── auth.ts                 # 认证中间件
├── hooks/
│   └── useAuth.ts              # 客户端认证 Hook
├── components/
│   └── auth/
│       └── AuthProvider.tsx    # 认证上下文提供者
└── app/api/auth/
    ├── login/route.ts          # 登录端点
    ├── logout/route.ts         # 登出端点
    ├── refresh/route.ts        # Token 刷新端点
    └── me/route.ts             # 获取当前用户信息
```

## 使用方法

### 1. 在应用根部添加 AuthProvider

```tsx
// app/layout.tsx
import { AuthProvider } from '@/components/auth/AuthProvider';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html>
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
```

### 2. 在组件中使用认证

```tsx
import { useAuthContext } from '@/components/auth/AuthProvider';

function MyComponent() {
  const { user, login, logout, authenticatedFetch } = useAuthContext();

  // 登录
  const handleLogin = async () => {
    const result = await login('<EMAIL>', 'password');
    if (result.success) {
      console.log('登录成功');
    }
  };

  // 调用需要认证的 API
  const callAPI = async () => {
    const response = await authenticatedFetch('/api/protected-endpoint');
    const data = await response.json();
  };

  return (
    <div>
      {user ? (
        <div>
          <p>欢迎, {user.email}</p>
          <button onClick={logout}>登出</button>
        </div>
      ) : (
        <button onClick={handleLogin}>登录</button>
      )}
    </div>
  );
}
```

### 3. 服务端 API 中获取用户信息

```tsx
// app/api/some-endpoint/route.ts
import { getCurrentUser } from '@/lib/auth';

export async function GET() {
  const user = await getCurrentUser();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // 用户已认证，继续处理
  return NextResponse.json({ data: 'some data' });
}
```

## API 端点

### POST /api/auth/login
登录用户并设置认证 cookies。

**请求体:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应:**
```json
{
  "success": true,
  "user": {
    "userId": 1,
    "email": "<EMAIL>"
  }
}
```

### POST /api/auth/logout
登出用户并清除认证 cookies。

### POST /api/auth/refresh
刷新 access token。

**响应:**
```json
{
  "success": true,
  "user": {
    "userId": 1,
    "email": "<EMAIL>"
  }
}
```

### GET /api/auth/me
获取当前用户信息。

## 中间件配置

中间件自动处理以下路径的认证：
- `/api/analysis/*` - 分析相关 API
- `/api/tasks/*` - 任务相关 API
- `/api/workflows/*` - 工作流相关 API
- `/dashboard` - 仪表板页面
- `/profile` - 用户资料页面

公开路径（不需要认证）：
- `/api/auth/*` - 认证相关 API
- `/login` - 登录页面
- `/register` - 注册页面
- `/` - 首页

## 环境变量

在 `.env.local` 中设置以下变量：

```env
JWT_SECRET=your-jwt-secret-key-change-this-in-production
REFRESH_SECRET=your-refresh-secret-key-change-this-in-production
```

## 安全注意事项

1. **生产环境**: 确保在生产环境中使用强密钥
2. **HTTPS**: 在生产环境中启用 HTTPS
3. **Cookie 安全**: 在生产环境中启用 `secure` 标志
4. **密钥轮换**: 定期轮换 JWT 密钥
5. **监控**: 监控异常的认证活动

## 故障排除

### Token 刷新失败
- 检查 refresh token 是否过期
- 验证环境变量是否正确设置
- 查看服务器日志中的错误信息

### 中间件不工作
- 检查 `middleware.ts` 文件是否在项目根目录
- 验证路径匹配配置是否正确
- 确保中间件函数正确导出

### Cookie 问题
- 检查浏览器是否支持 cookies
- 验证 SameSite 和 Secure 设置
- 确保域名配置正确

## 测试

使用提供的 `AuthExample` 组件测试认证功能：

```tsx
import { AuthExample } from '@/components/examples/AuthExample';

export default function TestPage() {
  return <AuthExample />;
}
```

这个组件展示了完整的认证流程，包括登录、登出和调用受保护的 API。