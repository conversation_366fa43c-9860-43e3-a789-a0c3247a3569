/**
 * 演示页面：展示 parseReportContent 功能和原文查看功能
 * 这个页面用于测试和演示报告内容解析和原文查看的功能
 */

'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { EyeIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';

// 模拟 parseReportContent 函数
const parseReportContent = (content: string) => {
  if (!content) return { conclusion: '', details: '', originalContent: '' };

  const lines = content.split('\n');
  const firstLine = lines[0] || '';

  // 尝试提取结论（通常在第一行或包含特定关键词）
  const conclusionMatch = firstLine.match(/^(买入|卖出|持有|中性|看涨|看跌|积极|消极)\s*-?\s*(.*)/);

  if (conclusionMatch) {
    return {
      conclusion: conclusionMatch[1],
      details: conclusionMatch[2] + '\n' + lines.slice(1).join('\n'),
      originalContent: content, // 保存原始内容
    };
  }

  return {
    conclusion: firstLine.length > 100 ? firstLine.substring(0, 100) + '...' : firstLine,
    details: lines.slice(1).join('\n'),
    originalContent: content, // 保存原始内容
  };
};

// 模拟报告数据
const mockReports = [
  {
    id: '1',
    title: '技术分析报告',
    content: `买入 - 基于技术分析，该股票呈现强烈的上涨趋势
技术指标分析：
1. MACD指标显示金叉信号，表明短期趋势向好
2. RSI指标处于50-70区间，显示健康的上涨动能
3. 成交量持续放大，确认了价格上涨的有效性

支撑位和阻力位分析：
- 当前支撑位：$45.20
- 下一个阻力位：$52.80
- 突破阻力位后目标价：$58.00

风险提示：
需要关注整体市场情绪变化，如果大盘出现调整，个股可能会受到影响。建议设置止损位在$43.50。`,
  },
  {
    id: '2',
    title: '基本面分析报告',
    content: `这是一份详细的基本面分析报告，涵盖了公司的财务状况、行业地位和未来发展前景
财务分析：
- 营收增长率：15.2%（同比）
- 净利润率：12.8%
- 资产负债率：35.6%
- 现金流状况：良好

行业分析：
该公司所处的科技行业正在经历快速发展期，市场需求旺盛。公司在细分领域具有技术优势，市场份额稳步提升。

竞争优势：
1. 强大的研发团队和技术实力
2. 完善的产品线和服务体系
3. 良好的客户关系和品牌声誉

未来展望：
预计未来三年公司将保持稳定增长，新产品线的推出将为公司带来新的增长点。`,
  },
];

export default function DemoReportContent() {
  // 状态管理：跟踪哪些报告显示原始内容
  const [showOriginalContent, setShowOriginalContent] = useState<Set<string>>(new Set());

  // 切换原始内容显示
  const toggleOriginalContent = (reportId: string) => {
    const newShowOriginal = new Set(showOriginalContent);
    if (newShowOriginal.has(reportId)) {
      newShowOriginal.delete(reportId);
    } else {
      newShowOriginal.add(reportId);
    }
    setShowOriginalContent(newShowOriginal);
  };

  // 渲染内容（简单的换行处理）
  const renderContent = (content: string) => {
    return content.replace(/\n/g, '<br>');
  };

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 p-6">
      <div className="max-w-4xl mx-auto">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-slate-900 dark:text-white">
              报告内容解析功能演示
            </CardTitle>
            <p className="text-slate-600 dark:text-slate-400">
              这个页面演示了 parseReportContent 函数的功能，包括内容解析和原文查看功能。
            </p>
          </CardHeader>
        </Card>

        <div className="space-y-6">
          {mockReports.map((report) => {
            const { conclusion, details, originalContent } = parseReportContent(report.content);
            const isShowingOriginal = showOriginalContent.has(report.id);

            return (
              <Card key={report.id} className="overflow-hidden">
                <CardHeader className="bg-slate-100 dark:bg-slate-800">
                  <CardTitle className="text-lg font-semibold text-slate-900 dark:text-white">
                    {report.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  {/* 结论摘要 */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
                    <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                      分析结论
                    </h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">{conclusion}</p>
                  </div>

                  {/* 详细内容 */}
                  <div className="border-t border-slate-200 dark:border-slate-700 pt-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        {isShowingOriginal ? '原始内容' : '详细分析'}
                      </h4>
                      {/* 切换原文按钮 */}
                      <button
                        onClick={() => toggleOriginalContent(report.id)}
                        className="flex items-center space-x-1 px-3 py-1 text-xs text-slate-500 hover:text-slate-700 dark:hover:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 rounded transition-colors"
                        title={isShowingOriginal ? '查看解析内容' : '查看原始内容'}
                      >
                        <EyeIcon className="h-3 w-3" />
                        <span>{isShowingOriginal ? '解析版' : '原文'}</span>
                      </button>
                    </div>
                    <div
                      className="text-sm text-slate-700 dark:text-slate-300 leading-relaxed bg-slate-50 dark:bg-slate-800 p-4 rounded-lg"
                      dangerouslySetInnerHTML={{
                        __html: renderContent(isShowingOriginal ? originalContent : details),
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* 功能说明 */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-slate-900 dark:text-white">
              功能说明
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-slate-600 dark:text-slate-400">
              <p>
                <strong>解析功能：</strong>
                parseReportContent 函数会自动识别报告中的结论关键词（如"买入"、"卖出"等），
                并将内容分为结论和详细分析两部分。
              </p>
              <p>
                <strong>原文查看：</strong>
                点击右上角的"原文"按钮可以查看完整的原始内容，
                点击"解析版"可以回到解析后的格式化内容。
              </p>
              <p>
                <strong>智能裁剪：</strong>
                如果标题超过100个字符，会自动裁剪并添加省略号，
                但原始内容始终完整保存。
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
