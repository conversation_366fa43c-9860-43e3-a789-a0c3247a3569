/**
 * Risk Data Management Page
 * 风险数据管理页面
 *
 * 集成所有风险数据存储与历史记录功能
 */

'use client';

import { RiskComparisonChart } from '@/components/risk/RiskComparisonChart';
import { RiskHistoryViewer } from '@/components/risk/RiskHistoryViewer';
import { RiskMetricsTrend } from '@/components/risk/RiskMetricsTrend';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useRiskStatistics } from '@/hooks/useRiskDataStorage';
import {
  AlertTriangle,
  BarChart3,
  CheckCircle,
  Clock,
  Database,
  Download,
  History,
  TrendingUp,
} from 'lucide-react';
import { useState } from 'react';

/**
 * 风险数据管理主页面
 * 需求 9: 风险数据存储与历史记录
 */
export default function RiskDataPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [statisticsParams, setStatisticsParams] = useState({});

  // 获取风险统计信息
  const { data: statistics, isLoading: statsLoading } = useRiskStatistics(statisticsParams);

  // 导出数据
  const handleExportData = async () => {
    try {
      const response = await fetch('/api/risk-data?export=true&format=json');
      const result = await response.json();

      if (result.success) {
        // 创建下载链接
        const dataStr = JSON.stringify(result.data.data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `risk-data-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Database className="w-8 h-8 text-blue-600" />
            风险数据管理
          </h1>
          <p className="text-gray-600 mt-2">风险分析结果存储、历史记录查询、对比分析和趋势监控</p>
        </div>

        <Button onClick={handleExportData} className="flex items-center gap-2">
          <Download className="w-4 h-4" />
          导出数据
        </Button>
      </div>

      {/* 统计概览卡片 */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总评估数</p>
                  <p className="text-2xl font-bold text-blue-600">{statistics.total_assessments}</p>
                </div>
                <Database className="w-8 h-8 text-blue-600 opacity-80" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">平均风险评分</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {statistics.avg_risk_score.toFixed(1)}/10
                  </p>
                </div>
                <AlertTriangle className="w-8 h-8 text-yellow-600 opacity-80" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">成功率</p>
                  <p className="text-2xl font-bold text-green-600">
                    {(statistics.success_rate * 100).toFixed(1)}%
                  </p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600 opacity-80" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">平均执行时间</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {(statistics.avg_execution_time_ms / 1000).toFixed(1)}s
                  </p>
                </div>
                <Clock className="w-8 h-8 text-purple-600 opacity-80" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 风险等级分布 */}
      {statistics?.risk_level_distribution && (
        <Card>
          <CardHeader>
            <CardTitle>风险等级分布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {statistics.risk_level_distribution.low || 0}
                </div>
                <div className="text-sm text-green-700">低风险</div>
                <div className="text-xs text-gray-600 mt-1">
                  {statistics.total_assessments > 0
                    ? (
                        ((statistics.risk_level_distribution.low || 0) /
                          statistics.total_assessments) *
                        100
                      ).toFixed(1)
                    : 0}
                  %
                </div>
              </div>

              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">
                  {statistics.risk_level_distribution.medium || 0}
                </div>
                <div className="text-sm text-yellow-700">中风险</div>
                <div className="text-xs text-gray-600 mt-1">
                  {statistics.total_assessments > 0
                    ? (
                        ((statistics.risk_level_distribution.medium || 0) /
                          statistics.total_assessments) *
                        100
                      ).toFixed(1)
                    : 0}
                  %
                </div>
              </div>

              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {statistics.risk_level_distribution.high || 0}
                </div>
                <div className="text-sm text-red-700">高风险</div>
                <div className="text-xs text-gray-600 mt-1">
                  {statistics.total_assessments > 0
                    ? (
                        ((statistics.risk_level_distribution.high || 0) /
                          statistics.total_assessments) *
                        100
                      ).toFixed(1)
                    : 0}
                  %
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 主要功能标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Database className="w-4 h-4" />
            概览
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="w-4 h-4" />
            历史记录
          </TabsTrigger>
          <TabsTrigger value="comparison" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            对比分析
          </TabsTrigger>
          <TabsTrigger value="trends" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            趋势分析
          </TabsTrigger>
        </TabsList>

        {/* 概览标签页 */}
        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>功能概览</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">数据存储功能</h3>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      风险评估结果自动存储
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      风险指标历史数据记录
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      评估参数和配置保存
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      数据完整性和一致性维护
                    </li>
                  </ul>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">分析功能</h3>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      多工作流风险对比
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      风险指标趋势分析
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      历史记录查询和筛选
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      数据导出和备份
                    </li>
                  </ul>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-900 mb-2">使用说明</h4>
                <p className="text-sm text-blue-800">
                  本系统自动存储所有风险分析结果，支持历史记录查询、多维度对比分析和趋势监控。
                  您可以通过不同标签页访问各项功能，或使用导出功能备份数据。
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 历史记录标签页 */}
        <TabsContent value="history">
          <RiskHistoryViewer />
        </TabsContent>

        {/* 对比分析标签页 */}
        <TabsContent value="comparison">
          <RiskComparisonChart />
        </TabsContent>

        {/* 趋势分析标签页 */}
        <TabsContent value="trends">
          <RiskMetricsTrend />
        </TabsContent>
      </Tabs>
    </div>
  );
}
