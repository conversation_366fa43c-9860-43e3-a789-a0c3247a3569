import { NextRequest, NextResponse } from 'next/server';
import { validateEmailFormat } from '@/lib/email';
import { verifyVerificationCode, getRecentVerificationCode } from '@/lib/verification-code-db';
import { error, success } from '@/lib/api-helpers';

export async function POST(request: NextRequest) {
  try {
    const { email, code, type = 'register' } = await request.json();

    // 验证输入
    if (!email || !code) {
      return error('请输入邮箱和验证码', 400);
    }

    if (!validateEmailFormat(email)) {
      return error('请输入有效的邮箱地址', 400);
    }

    // 验证验证码格式（6位数字）
    if (!/^\d{6}$/.test(code)) {
      return error('验证码格式错误，请输入6位数字', 400);
    }

    // 验证验证码类型
    const validTypes = ['register', 'reset_password', 'change_email'];
    if (!validTypes.includes(type)) {
      return error('无效的验证码类型', 400);
    }

    // 验证验证码
    const verifyResult = await verifyVerificationCode(email, code, type);

    // 根据验证结果返回不同的响应
    switch (verifyResult) {
      case 'success':
        return success(
          {
            email,
            type,
            verified: true,
          },
          '验证码验证成功'
        );

      case 'invalid':
        return error('验证码错误', 400);

      case 'expired':
        return error('验证码已过期，请重新获取', 400);

      case 'used':
        return error('验证码已使用，请重新获取', 400);

      case 'max_attempts':
        return error('验证失败次数过多，请重新获取验证码', 400);

      default:
        return error('验证失败', 400);
    }
  } catch (err: any) {
    console.error('验证验证码失败:', err);
    return error('服务器内部错误', 500);
  }
}

// 获取验证码状态的GET接口
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    const type = searchParams.get('type') || 'register';

    if (!email) {
      return error('请提供邮箱地址', 400);
    }

    if (!validateEmailFormat(email)) {
      return error('请输入有效的邮箱地址', 400);
    }

    // 验证验证码类型
    const validTypes = ['register', 'reset_password', 'change_email'];
    if (!validTypes.includes(type)) {
      return error('无效的验证码类型', 400);
    }

    // 获取最近的验证码记录
    const recentCode = await getRecentVerificationCode(email, type as any, 60);

    if (!recentCode) {
      return success(
        {
          hasCode: false,
          email,
          type,
        },
        '没有找到有效的验证码'
      );
    }

    const now = new Date();
    const isExpired = recentCode.expires_at < now;
    const remainingTime = isExpired ? 0 : Math.ceil((recentCode.expires_at.getTime() - now.getTime()) / 1000 / 60);

    return success(
      {
        hasCode: true,
        email,
        type,
        isUsed: recentCode.is_used,
        isExpired,
        attempts: recentCode.attempts,
        maxAttempts: recentCode.max_attempts,
        remainingMinutes: remainingTime,
        createdAt: recentCode.created_at,
        expiresAt: recentCode.expires_at,
      },
      '验证码状态获取成功'
    );
  } catch (err: any) {
    console.error('获取验证码状态失败:', err);
    return error('服务器内部错误', 500);
  }
}