/**
 * English SEO utility functions
 */

// Generate English page titles
export function generateEnglishPageTitle(pageTitle?: string, siteName = 'TradingAgents') {
  if (!pageTitle) return `${siteName} - AI-Powered Stock Analysis Platform`;
  return `${pageTitle} | ${siteName} - AI Stock Analysis Platform`;
}

// Generate English page descriptions
export function generateEnglishPageDescription(customDescription?: string) {
  return (
    customDescription ||
    'Professional AI-driven stock analysis platform powered by multi-agent large language models, providing comprehensive intelligent analysis for investment decisions.'
  );
}

// Generate English keywords
export function generateEnglishKeywords(pageKeywords: string[] = []) {
  const baseKeywords = [
    'stock analysis',
    'AI investment',
    'smart investing',
    'stock recommendations',
    'investment decisions',
    'multi-agent',
    'large language models',
    'LangGraph',
    'fintech',
    'financial technology',
    'market analysis',
    'technical analysis',
    'fundamental analysis',
    'news analysis',
    'sentiment analysis',
    'risk management',
    'investment strategy',
    'stock prediction',
    'quantitative investing',
    'robo advisor',
    'TradingAgents',
    'trading agents',
    'financial AI',
    'investment AI',
    'stock AI',
    'automated trading',
    'algorithmic trading',
    'portfolio management',
    'financial analytics',
    'machine learning trading',
    'AI stock picker',
    'intelligent portfolio',
    'financial data analysis',
  ];

  const combined = [...pageKeywords, ...baseKeywords];
  return Array.from(new Set(combined));
}

// Generate English structured data - Software Application
export function generateEnglishSoftwareStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: 'TradingAgents',
    applicationCategory: 'FinanceApplication',
    description:
      'Professional AI-driven stock analysis platform powered by multi-agent large language models, providing comprehensive intelligent analysis for investment decisions.',
    url: 'https://tradingagent.top/en',
    image: 'https://tradingagent.top/tradingAgentLogoWithBg.png',
    author: {
      '@type': 'Organization',
      name: 'TradingAgents Team',
      url: 'https://tradingagent.top/en',
    },
    publisher: {
      '@type': 'Organization',
      name: 'TradingAgents',
      logo: {
        '@type': 'ImageObject',
        url: 'https://tradingagent.top/tradingAgentLogo.png',
        width: 512,
        height: 512,
      },
    },
    operatingSystem: 'Web',
    softwareVersion: '1.0.0',
    datePublished: '2024-01-01',
    dateModified: new Date().toISOString().split('T')[0],
    inLanguage: 'en-US',
    features: [
      'AI-powered stock analysis',
      'Multi-agent collaborative analysis',
      'Market sentiment analysis',
      'Technical analysis',
      'Fundamental analysis',
      'News intelligence analysis',
      'Social media analysis',
      'Risk management',
      'Investment decision support',
      'Real-time market data',
      'Portfolio optimization',
      'Automated reporting',
    ],
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      ratingCount: '156',
      bestRating: '5',
      worstRating: '1',
    },
  };
}

// Generate English Organization structured data
export function generateEnglishOrganizationStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'TradingAgents',
    url: 'https://tradingagent.top/en',
    logo: 'https://tradingagent.top/tradingAgentLogo.png',
    description: 'Professional AI-driven stock analysis platform',
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'US',
    },
    sameAs: ['https://github.com/bkf777/tradingAgent', 'https://twitter.com/TradingAgents'],
    foundingDate: '2024-01-01',
    industry: 'Financial Technology',
    numberOfEmployees: '10-50',
    keywords: 'AI, Machine Learning, Stock Analysis, Financial Technology, Investment',
  };
}

// Generate English Website structured data
export function generateEnglishWebsiteStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'TradingAgents',
    url: 'https://tradingagent.top/en',
    description: 'AI-powered stock analysis platform',
    inLanguage: 'en-US',
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://tradingagent.top/en/search?q={search_term_string}',
      'query-input': 'required name=search_term_string',
    },
    publisher: {
      '@type': 'Organization',
      name: 'TradingAgents',
    },
  };
}

// Generate English FAQ structured data
export function generateEnglishFAQStructuredData() {
  const faqs = [
    {
      question: 'What is TradingAgents?',
      answer:
        'TradingAgents is a professional AI-driven stock analysis platform that uses multi-agent large language models to provide comprehensive investment analysis and recommendations.',
    },
    {
      question: 'How does the AI analysis work?',
      answer:
        'Our platform employs multiple specialized AI agents including market analysts, news analysts, social media analysts, and fundamental analysts that work together to provide multi-dimensional stock analysis.',
    },
    {
      question: 'Is the platform suitable for beginners?',
      answer:
        'Yes, TradingAgents is designed for both beginners and experienced investors. The platform provides clear, easy-to-understand analysis reports and investment recommendations.',
    },
    {
      question: 'What types of analysis does the platform provide?',
      answer:
        'We provide technical analysis, fundamental analysis, news sentiment analysis, social media sentiment analysis, and comprehensive risk assessment for stocks.',
    },
    {
      question: 'How accurate are the AI predictions?',
      answer:
        'Our AI models have demonstrated high accuracy in market analysis, with continuous learning and improvement based on market data and feedback.',
    },
  ];

  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
}

// Create SEO-friendly English URLs
export function createEnglishSEOFriendlyUrl(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/--+/g, '-') // Merge multiple hyphens
    .trim();
}

// Get full English URL
export function getEnglishFullUrl(path: string = ''): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://tradingagent.top';
  const englishPath = path.startsWith('/en')
    ? path
    : `/en${path.startsWith('/') ? path : `/${path}`}`;
  return `${baseUrl}${englishPath}`;
}
