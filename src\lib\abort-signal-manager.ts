// AbortSignal 管理器 - 统一管理 AbortSignal 避免内存泄漏

class AbortSignalManager {
  private static instance: AbortSignalManager | null = null;
  private abortController: AbortController | null = null;
  private listeners: Set<() => void> = new Set();

  private constructor() {
    this.createController();
  }

  public static getInstance(): AbortSignalManager {
    if (!AbortSignalManager.instance) {
      AbortSignalManager.instance = new AbortSignalManager();
    }
    return AbortSignalManager.instance;
  }

  private createController(): void {
    if (this.abortController) return;
    
    this.abortController = new AbortController();
    this.abortController.signal.addEventListener('abort', () => {
      this.listeners.forEach(listener => {
        try {
          listener();
        } catch (error) {
          console.error('AbortSignal 监听器执行失败:', error);
        }
      });
    });
  }

  public getSignal(): AbortSignal {
    if (!this.abortController) this.createController();
    return this.abortController!.signal;
  }

  public addListener(listener: () => void): void {
    this.listeners.add(listener);
  }

  public removeListener(listener: () => void): void {
    this.listeners.delete(listener);
  }

  public abort(reason?: any): void {
    if (this.abortController && !this.abortController.signal.aborted) {
      this.abortController.abort(reason);
    }
  }

  public cleanup(): void {
    if (this.abortController && !this.abortController.signal.aborted) {
      this.abortController.abort('清理信号管理器');
    }
    this.listeners.clear();
    this.abortController = null;
    AbortSignalManager.instance = null;
  }
}

export const abortSignalManager = AbortSignalManager.getInstance();
export const getGlobalAbortSignal = () => abortSignalManager.getSignal();
export const cleanupAbortSignal = () => abortSignalManager.cleanup();

// 调试函数
export function getAbortSignalStats(): {
  listenerCount: number;
  isAborted: boolean;
} {
  return {
    listenerCount: abortSignalManager['listeners'].size,
    isAborted: abortSignalManager['abortController']?.signal.aborted || false,
  };
}