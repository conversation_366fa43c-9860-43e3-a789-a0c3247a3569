import { NextRequest, NextResponse } from 'next/server';
import { authMiddleware } from './src/middleware/auth';

export async function middleware(request: NextRequest) {
  // 应用认证中间件
  return await authMiddleware(request);
}

// 配置中间件匹配的路径
export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api/auth/login, api/auth/register (认证相关)
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico (网站图标)
     * - public 文件夹中的文件
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};