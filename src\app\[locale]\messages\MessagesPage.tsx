'use client';

import { Message, messageApi } from '@/lib/api';
import useUserStore from '@/store/userStore';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useCallback, useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// 消息类型配置
const MESSAGE_TYPES = {
  human: { label: '用户', color: 'bg-blue-100 text-blue-800 border-blue-200', icon: '👤' },
  ai: { label: 'AI', color: 'bg-green-100 text-green-800 border-green-200', icon: '🤖' },
  system: { label: '系统', color: 'bg-gray-100 text-gray-800 border-gray-200', icon: '⚙️' },
  tool: { label: '工具', color: 'bg-purple-100 text-purple-800 border-purple-200', icon: '🔧' },
};

function MessagesPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useUserStore();

  // 状态管理
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 20,
    offset: 0,
    hasMore: false,
    page: 1,
    totalPages: 0,
  });

  // 筛选状态
  const [filters, setFilters] = useState({
    taskId: searchParams?.get('task_id') || '',
    messageType: searchParams?.get('message_type') || '',
    conversationId: searchParams?.get('conversation_id') || '',
    startDate: searchParams?.get('start_date') || '',
    endDate: searchParams?.get('end_date') || '',
  });

  // 获取消息列表
  const fetchMessages = useCallback(
    async (page: number = 1) => {
      if (!user?.id) {
        setError('用户未登录');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const params = {
          page,
          limit: pagination.limit,
          user_id: user.id,
          ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value !== '')),
        };

        const response = await messageApi.getMessages(params);
        setMessages(response.data?.messages || []);
        if (response.data?.pagination) {
          setPagination({
            ...response.data.pagination,
            page: Math.floor(response.data.pagination.offset / response.data.pagination.limit) + 1,
            totalPages: Math.ceil(response.data.pagination.total / response.data.pagination.limit),
          });
        }
      } catch (error) {
        console.error('获取消息失败:', error);
        setError('获取消息失败');
        toast.error('获取消息失败');
      } finally {
        setLoading(false);
      }
    },
    [user?.id, filters, pagination.limit]
  );

  // 初始化加载
  useEffect(() => {
    fetchMessages(1);
  }, [fetchMessages]);

  // 处理筛选器变化
  const handleFilterChange = useCallback((key: string, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  }, []);

  // 应用筛选器
  const applyFilters = useCallback(() => {
    fetchMessages(1);
  }, [fetchMessages]);

  // 清除筛选器
  const clearFilters = useCallback(() => {
    setFilters({
      taskId: '',
      messageType: '',
      conversationId: '',
      startDate: '',
      endDate: '',
    });
  }, []);

  // 处理分页
  const handlePageChange = useCallback(
    (page: number) => {
      fetchMessages(page);
    },
    [fetchMessages]
  );

  // 格式化消息内容
  const formatMessageContent = useCallback((content: string, messageType: string) => {
    if (messageType === 'tool') {
      try {
        const parsed = JSON.parse(content);
        return JSON.stringify(parsed, null, 2);
      } catch {
        return content;
      }
    }
    return content;
  }, []);

  // 渲染消息卡片
  const renderMessageCard = useCallback(
    (message: Message) => {
      const typeConfig =
        MESSAGE_TYPES[message.message_type as keyof typeof MESSAGE_TYPES] || MESSAGE_TYPES.system;

      return (
        <div
          key={message.id}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          {/* 消息头部 */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${typeConfig.color}`}
              >
                <span className="mr-1">{typeConfig.icon}</span>
                {typeConfig.label}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                序号: {message.sequence_number}
              </span>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {format(new Date(message.created_at), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
            </div>
          </div>

          {/* 消息元信息 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">任务ID:</span>
              <span className="ml-2 text-gray-600 dark:text-gray-400">{message.task_id}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">会话ID:</span>
              <span className="ml-2 text-gray-600 dark:text-gray-400">{message.task_id}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">消息ID:</span>
              <span className="ml-2 text-gray-600 dark:text-gray-400">{message.message_id}</span>
            </div>
          </div>

          {/* 消息内容 */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <div className="prose dark:prose-invert max-w-none">
              {message.message_type === 'tool' ? (
                <pre className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{formatMessageContent(message.content, message.message_type)}</code>
                </pre>
              ) : (
                <ReactMarkdown remarkPlugins={[remarkGfm]}>{message.content}</ReactMarkdown>
              )}
            </div>
          </div>

          {/* 元数据 */}
          {message.metadata && Object.keys(message.metadata).length > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <details className="group">
                <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100">
                  元数据 ({Object.keys(message.metadata).length} 项)
                </summary>
                <pre className="mt-2 bg-gray-50 dark:bg-gray-900 p-3 rounded text-xs overflow-x-auto">
                  {JSON.stringify(message.metadata, null, 2)}
                </pre>
              </details>
            </div>
          )}
        </div>
      );
    },
    [formatMessageContent]
  );

  if (loading && messages.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">{error}</div>
          <button
            onClick={() => fetchMessages(1)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">消息查询</h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2">
            查看和管理系统消息、分析报告和智能体通信记录
          </p>
        </div>

        {/* 筛选器 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                任务ID
              </label>
              <input
                type="text"
                value={filters.taskId}
                onChange={(e) => handleFilterChange('taskId', e.target.value)}
                placeholder="输入任务ID"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                消息类型
              </label>
              <select
                value={filters.messageType}
                onChange={(e) => handleFilterChange('messageType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">全部类型</option>
                <option value="human">用户</option>
                <option value="ai">AI</option>
                <option value="system">系统</option>
                <option value="tool">工具</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                会话ID
              </label>
              <input
                type="text"
                value={filters.conversationId}
                onChange={(e) => handleFilterChange('conversationId', e.target.value)}
                placeholder="输入会话ID"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                开始日期
              </label>
              <input
                type="date"
                value={filters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                结束日期
              </label>
              <input
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div className="flex items-end space-x-2">
              <button
                onClick={applyFilters}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                应用筛选
              </button>
              <button
                onClick={clearFilters}
                className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
              >
                清除
              </button>
            </div>
          </div>
        </div>

        {/* 消息统计 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              共找到 {pagination.total} 条消息，当前第 {pagination.page} 页，共{' '}
              {pagination.totalPages} 页
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              每页显示 {pagination.limit} 条
            </div>
          </div>
        </div>

        {/* 消息列表 */}
        <div className="space-y-4">
          {messages.length === 0 ? (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-12 text-center">
              <div className="text-gray-400 text-lg mb-4">暂无消息</div>
              <p className="text-gray-500">尝试调整筛选条件或创建新的分析任务</p>
            </div>
          ) : (
            messages.map(renderMessageCard)
          )}
        </div>

        {/* 分页 */}
        {pagination.totalPages > 1 && (
          <div className="mt-8 flex justify-center">
            <nav className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page <= 1}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700"
              >
                上一页
              </button>

              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const page = i + Math.max(1, pagination.page - 2);
                if (page > pagination.totalPages) return null;

                return (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-3 py-2 text-sm font-medium rounded-md ${
                      page === pagination.page
                        ? 'text-blue-600 bg-blue-50 border border-blue-300 dark:bg-blue-900 dark:text-blue-300'
                        : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700'
                    }`}
                  >
                    {page}
                  </button>
                );
              })}

              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700"
              >
                下一页
              </button>
            </nav>
          </div>
        )}
      </div>
    </div>
  );
}

export default function MessagesPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      }
    >
      <MessagesPageContent />
    </Suspense>
  );
}
