# GitHub Secrets 和环境变量配置指南

本文档说明了如何配置 TradingAgents Frontend 项目的 GitHub Secrets 和环境变量。

## 必需的 GitHub Secrets

### 1. 数据库相关密钥

#### 预发布环境

```
MYSQL_ROOT_PASSWORD_STAGING=your_staging_root_password
MYSQL_PASSWORD_STAGING=your_staging_user_password
```

#### 生产环境

```
MYSQL_ROOT_PASSWORD_PROD=your_production_root_password
MYSQL_PASSWORD_PROD=your_production_user_password
```

### 2. API 密钥

```
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key
NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_api_key
```

### 3. 容器镜像注册表密钥

#### 阿里云容器镜像服务

```
ALIYUN_REGISTRY_USERNAME=aliyun1315382626
ALIYUN_REGISTRY_PASSWORD=ezreal123
```

#### 其他私有注册表（可选）

```
# 服务器 SSH 密钥（如果使用 SSH 部署）
DEPLOY_SSH_KEY=your_private_ssh_key
DEPLOY_HOST=your_server_host
DEPLOY_USER=your_deploy_user

# 其他 Docker Registry 密钥
DOCKER_REGISTRY_USERNAME=your_registry_username
DOCKER_REGISTRY_PASSWORD=your_registry_password
```

### 4. 监控和通知

```
# Lighthouse CI Token（性能测试）
LHCI_GITHUB_APP_TOKEN=your_lighthouse_ci_token

# Slack 通知（可选）
SLACK_WEBHOOK_URL=your_slack_webhook_url

# 邮件通知（可选）
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password
```

## 环境变量配置

### 开发环境 (.env.local)

```bash
# API 配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:5000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
BACK_END_URL=http://localhost:5000

# 数据库配置
MYSQL_ROOT_PASSWORD=trading123
MYSQL_DATABASE=trading_analysis_dev
MYSQL_USER=trading_user
MYSQL_PASSWORD=trading123

# API 密钥
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key
NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_api_key

# 调试模式
DEBUG=true
NODE_ENV=development
```

### 预发布环境 (.env.staging)

```bash
# API 配置
NEXT_PUBLIC_API_BASE_URL=https://api-staging.tradingagents.example.com
NEXT_PUBLIC_WS_URL=wss://ws-staging.tradingagents.example.com
BACK_END_URL=https://api-staging.tradingagents.example.com

# 数据库配置
MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD_STAGING}
MYSQL_DATABASE=trading_analysis_staging
MYSQL_USER=trading_user
MYSQL_PASSWORD=${MYSQL_PASSWORD_STAGING}

# API 密钥
NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
NEXT_PUBLIC_FINNHUB_API_KEY=${NEXT_PUBLIC_FINNHUB_API_KEY}

# 环境标识
NODE_ENV=production
ENVIRONMENT=staging
```

### 生产环境 (.env.production)

```bash
# API 配置
NEXT_PUBLIC_API_BASE_URL=https://api.tradingagents.example.com
NEXT_PUBLIC_WS_URL=wss://ws.tradingagents.example.com
BACK_END_URL=https://api.tradingagents.example.com

# 数据库配置
MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD_PROD}
MYSQL_DATABASE=trading_analysis
MYSQL_USER=trading_user
MYSQL_PASSWORD=${MYSQL_PASSWORD_PROD}

# API 密钥
NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
NEXT_PUBLIC_FINNHUB_API_KEY=${NEXT_PUBLIC_FINNHUB_API_KEY}

# 环境标识
NODE_ENV=production
ENVIRONMENT=production

# 性能优化
NEXT_TELEMETRY_DISABLED=1
```

## 配置步骤

### 1. 在 GitHub 仓库中设置 Secrets

1. 进入 GitHub 仓库页面
2. 点击 `Settings` 选项卡
3. 在左侧菜单中选择 `Secrets and variables` > `Actions`
4. 点击 `New repository secret` 添加密钥

### 2. 设置环境保护规则

1. 在 `Settings` > `Environments` 中创建环境：

   - `staging` - 预发布环境
   - `production` - 生产环境

2. 为生产环境设置保护规则：
   - 需要审批者确认
   - 限制部署分支为 `main`
   - 设置部署延迟时间

### 3. 配置环境特定的 Secrets

为每个环境配置特定的密钥：

#### Staging 环境

- `MYSQL_ROOT_PASSWORD_STAGING`
- `MYSQL_PASSWORD_STAGING`

#### Production 环境

- `MYSQL_ROOT_PASSWORD_PROD`
- `MYSQL_PASSWORD_PROD`

#### 阿里云容器镜像服务

- `ALIYUN_REGISTRY_USERNAME` - 阿里云容器镜像服务用户名
- `ALIYUN_REGISTRY_PASSWORD` - 阿里云容器镜像服务密码

## 安全最佳实践

### 1. 密钥轮换

- 定期更换数据库密码
- 定期更换 API 密钥
- 使用强密码生成器

### 2. 最小权限原则

- 为不同环境使用不同的数据库用户
- 限制 API 密钥的权限范围
- 使用只读密钥进行监控

### 3. 密钥管理

- 不要在代码中硬编码密钥
- 使用环境变量传递敏感信息
- 定期审计密钥使用情况

### 4. 监控和告警

- 监控异常的 API 调用
- 设置密钥泄露告警
- 记录所有部署操作

## 故障排除

### 常见问题

1. **密钥未生效**

   - 检查密钥名称是否正确
   - 确认环境配置是否正确
   - 重新运行工作流

2. **数据库连接失败**

   - 检查数据库密码是否正确
   - 确认数据库服务是否运行
   - 检查网络连接

3. **API 调用失败**
   - 验证 API 密钥是否有效
   - 检查 API 配额是否用完
   - 确认 API 端点是否正确

### 调试命令

```bash
# 检查环境变量
echo $NEXT_PUBLIC_API_BASE_URL

# 测试数据库连接
mysql -h localhost -u $MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE

# 测试 API 连接
curl -H "Authorization: Bearer $NEXT_PUBLIC_OPENAI_API_KEY" \
     https://api.nuwaapi.com/v1/models
```

## 联系支持

如果遇到配置问题，请：

1. 检查本文档的故障排除部分
2. 查看 GitHub Actions 的运行日志
3. 联系项目维护者获取帮助
